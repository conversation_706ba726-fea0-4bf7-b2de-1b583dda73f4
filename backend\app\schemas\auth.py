"""
مخططات المصادقة
"""
from typing import Optional
from pydantic import BaseModel, validator


class Token(BaseModel):
    """مخطط الرمز المميز"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """بيانات الرمز المميز"""
    username: Optional[str] = None


class UserLogin(BaseModel):
    """مخطط تسجيل الدخول"""
    username: str
    password: str
    
    @validator('username')
    def username_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('اسم المستخدم مطلوب')
        return v.strip()
    
    @validator('password')
    def password_must_not_be_empty(cls, v):
        if not v or len(v) < 6:
            raise ValueError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        return v


class UserRegister(BaseModel):
    """مخطط تسجيل مستخدم جديد"""
    username: str
    password: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    
    @validator('username')
    def username_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('اسم المستخدم مطلوب')
        if len(v.strip()) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        return v.strip()
    
    @validator('password')
    def password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        return v
    
    @validator('email')
    def email_validation(cls, v):
        if v and '@' not in v:
            raise ValueError('البريد الإلكتروني غير صالح')
        return v


class ChangePassword(BaseModel):
    """مخطط تغيير كلمة المرور"""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def new_password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل')
        return v
