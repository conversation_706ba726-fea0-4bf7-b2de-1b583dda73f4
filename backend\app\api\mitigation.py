from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models.user import User
from app.api.auth import get_current_active_user
from app.schemas.mitigation import (
    MitigationPlanCreate, MitigationPlanUpdate, MitigationPlanResponse, MitigationPlanListResponse,
    MitigationActionCreate, MitigationActionUpdate, MitigationActionResponse, MitigationActionListResponse,
    ActionUpdateCreate, ActionUpdateResponse, PlanReviewCreate, PlanReviewResponse,
    RiskTreatmentCreate, RiskTreatmentResponse, MitigationSummary, ActionsSummary
)
from app.crud import mitigation as mitigation_crud

router = APIRouter()


# Mitigation Plans
@router.get("/plans", response_model=dict)
async def get_mitigation_plans(
    risk_id: Optional[int] = Query(None, description="معرف المخاطرة"),
    owner_id: Optional[int] = Query(None, description="معرف المالك"),
    status: Optional[str] = Query(None, description="حالة الخطة"),
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(10, ge=1, le=100, description="عدد العناصر في الصفحة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على قائمة خطط التخفيف"""
    
    skip = (page - 1) * per_page
    plans = mitigation_crud.get_mitigation_plans(
        db, risk_id=risk_id, owner_id=owner_id, status=status,
        current_user=current_user, skip=skip, limit=per_page
    )
    
    # حساب العدد الإجمالي
    total_query = mitigation_crud.get_mitigation_plans(
        db, risk_id=risk_id, owner_id=owner_id, status=status,
        current_user=current_user, skip=0, limit=1000000
    )
    total = len(total_query)
    
    return {
        "items": plans,
        "total": total,
        "page": page,
        "per_page": per_page,
        "pages": (total + per_page - 1) // per_page
    }


@router.get("/plans/summary", response_model=MitigationSummary)
async def get_mitigation_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على ملخص خطط التخفيف"""
    return mitigation_crud.get_mitigation_summary(db, current_user)


@router.post("/plans", response_model=MitigationPlanResponse)
async def create_mitigation_plan(
    plan: MitigationPlanCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء خطة تخفيف جديدة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء خطط التخفيف"
        )
    
    # التحقق من وجود المخاطرة
    from app.crud.risk import get_risk
    risk = get_risk(db, plan.risk_id)
    if not risk:
        raise HTTPException(
            status_code=404,
            detail="المخاطرة المحددة غير موجودة"
        )
    
    # التحقق من وجود المالك
    from app.crud.users import get_user
    owner = get_user(db, plan.owner_id)
    if not owner:
        raise HTTPException(
            status_code=404,
            detail="المالك المحدد غير موجود"
        )
    
    return mitigation_crud.create_mitigation_plan(db, plan, current_user)


@router.get("/plans/{plan_id}", response_model=MitigationPlanResponse)
async def get_mitigation_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على خطة تخفيف بالمعرف"""
    
    plan = mitigation_crud.get_mitigation_plan(db, plan_id)
    if not plan:
        raise HTTPException(
            status_code=404,
            detail="خطة التخفيف غير موجودة"
        )
    
    # التحقق من الصلاحيات
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        plan.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض هذه الخطة"
        )
    
    return plan


@router.put("/plans/{plan_id}", response_model=MitigationPlanResponse)
async def update_mitigation_plan(
    plan_id: int,
    plan_update: MitigationPlanUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تحديث خطة تخفيف"""
    
    plan = mitigation_crud.update_mitigation_plan(db, plan_id, plan_update, current_user)
    if not plan:
        raise HTTPException(
            status_code=404,
            detail="خطة التخفيف غير موجودة أو ليس لديك صلاحية لتحديثها"
        )
    
    return plan


@router.delete("/plans/{plan_id}")
async def delete_mitigation_plan(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """حذف خطة تخفيف"""
    
    success = mitigation_crud.delete_mitigation_plan(db, plan_id, current_user)
    if not success:
        raise HTTPException(
            status_code=404,
            detail="خطة التخفيف غير موجودة أو ليس لديك صلاحية لحذفها"
        )
    
    return {"message": "تم حذف خطة التخفيف بنجاح"}


# Mitigation Actions
@router.get("/actions", response_model=dict)
async def get_mitigation_actions(
    plan_id: Optional[int] = Query(None, description="معرف الخطة"),
    assigned_to_id: Optional[int] = Query(None, description="معرف المكلف"),
    status: Optional[str] = Query(None, description="حالة الإجراء"),
    priority: Optional[str] = Query(None, description="أولوية الإجراء"),
    overdue_only: bool = Query(False, description="المتأخرة فقط"),
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(10, ge=1, le=100, description="عدد العناصر في الصفحة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على قائمة إجراءات التخفيف"""
    
    skip = (page - 1) * per_page
    actions = mitigation_crud.get_mitigation_actions(
        db, plan_id=plan_id, assigned_to_id=assigned_to_id, status=status,
        priority=priority, overdue_only=overdue_only, current_user=current_user,
        skip=skip, limit=per_page
    )
    
    # حساب العدد الإجمالي
    total_actions = mitigation_crud.get_mitigation_actions(
        db, plan_id=plan_id, assigned_to_id=assigned_to_id, status=status,
        priority=priority, overdue_only=overdue_only, current_user=current_user,
        skip=0, limit=1000000
    )
    total = len(total_actions)
    
    return {
        "items": actions,
        "total": total,
        "page": page,
        "per_page": per_page,
        "pages": (total + per_page - 1) // per_page
    }


@router.get("/actions/summary", response_model=ActionsSummary)
async def get_actions_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على ملخص الإجراءات"""
    return mitigation_crud.get_actions_summary(db, current_user)


@router.post("/actions", response_model=MitigationActionResponse)
async def create_mitigation_action(
    action: MitigationActionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء إجراء تخفيف جديد"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء إجراءات التخفيف"
        )
    
    # التحقق من وجود الخطة
    plan = mitigation_crud.get_mitigation_plan(db, action.plan_id)
    if not plan:
        raise HTTPException(
            status_code=404,
            detail="خطة التخفيف المحددة غير موجودة"
        )
    
    # التحقق من وجود المكلف
    from app.crud.users import get_user
    assigned_to = get_user(db, action.assigned_to_id)
    if not assigned_to:
        raise HTTPException(
            status_code=404,
            detail="المستخدم المكلف غير موجود"
        )
    
    return mitigation_crud.create_mitigation_action(db, action, current_user)


@router.get("/actions/{action_id}", response_model=MitigationActionResponse)
async def get_mitigation_action(
    action_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على إجراء تخفيف بالمعرف"""
    
    action = mitigation_crud.get_mitigation_action(db, action_id)
    if not action:
        raise HTTPException(
            status_code=404,
            detail="إجراء التخفيف غير موجود"
        )
    
    # التحقق من الصلاحيات
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        action.assigned_to_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض هذا الإجراء"
        )
    
    return action


@router.put("/actions/{action_id}", response_model=MitigationActionResponse)
async def update_mitigation_action(
    action_id: int,
    action_update: MitigationActionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تحديث إجراء تخفيف"""
    
    action = mitigation_crud.update_mitigation_action(db, action_id, action_update, current_user)
    if not action:
        raise HTTPException(
            status_code=404,
            detail="إجراء التخفيف غير موجود أو ليس لديك صلاحية لتحديثه"
        )
    
    return action


# Action Updates
@router.get("/actions/{action_id}/updates", response_model=List[ActionUpdateResponse])
async def get_action_updates(
    action_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على تحديثات الإجراء"""
    
    # التحقق من وجود الإجراء والصلاحيات
    action = mitigation_crud.get_mitigation_action(db, action_id)
    if not action:
        raise HTTPException(status_code=404, detail="إجراء التخفيف غير موجود")
    
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        action.assigned_to_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض تحديثات هذا الإجراء"
        )
    
    return mitigation_crud.get_action_updates(db, action_id)


@router.post("/actions/{action_id}/updates", response_model=ActionUpdateResponse)
async def create_action_update(
    action_id: int,
    update: ActionUpdateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء تحديث للإجراء"""
    
    # التحقق من وجود الإجراء
    action = mitigation_crud.get_mitigation_action(db, action_id)
    if not action:
        raise HTTPException(status_code=404, detail="إجراء التخفيف غير موجود")
    
    # التحقق من الصلاحيات
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        action.assigned_to_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لتحديث هذا الإجراء"
        )
    
    # تعديل معرف الإجراء في التحديث
    update.action_id = action_id
    
    return mitigation_crud.create_action_update(db, update, current_user)


# Plan Reviews
@router.get("/plans/{plan_id}/reviews", response_model=List[PlanReviewResponse])
async def get_plan_reviews(
    plan_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على مراجعات الخطة"""

    # التحقق من وجود الخطة والصلاحيات
    plan = mitigation_crud.get_mitigation_plan(db, plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")

    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and
        plan.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض مراجعات هذه الخطة"
        )

    return mitigation_crud.get_plan_reviews(db, plan_id)


@router.post("/plans/{plan_id}/reviews", response_model=PlanReviewResponse)
async def create_plan_review(
    plan_id: int,
    review: PlanReviewCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء مراجعة للخطة"""

    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء مراجعات الخطط"
        )

    # التحقق من وجود الخطة
    plan = mitigation_crud.get_mitigation_plan(db, plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="خطة التخفيف غير موجودة")

    # تعديل معرف الخطة في المراجعة
    review.plan_id = plan_id

    return mitigation_crud.create_plan_review(db, review, current_user)


# Risk Treatment
@router.post("/treatment", response_model=RiskTreatmentResponse)
async def create_risk_treatment(
    treatment: RiskTreatmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء معالجة للمخاطر"""

    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء معالجات المخاطر"
        )

    # التحقق من وجود المخاطرة
    from app.crud.risk import get_risk
    risk = get_risk(db, treatment.risk_id)
    if not risk:
        raise HTTPException(
            status_code=404,
            detail="المخاطرة المحددة غير موجودة"
        )

    return mitigation_crud.create_risk_treatment(db, treatment, current_user)


@router.get("/risks/{risk_id}/treatment", response_model=List[RiskTreatmentResponse])
async def get_risk_treatments(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على معالجات المخاطرة"""

    # التحقق من وجود المخاطرة والصلاحيات
    from app.crud.risk import get_risk
    risk = get_risk(db, risk_id)
    if not risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")

    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and
        risk.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض معالجات هذه المخاطرة"
        )

    from app.models.mitigation import RiskTreatment
    return db.query(RiskTreatment).filter(RiskTreatment.risk_id == risk_id).all()
