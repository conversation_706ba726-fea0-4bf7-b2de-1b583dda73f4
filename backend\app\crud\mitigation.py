from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.mitigation import (
    MitigationPlan, MitigationAction, ActionUpdate, PlanReview, RiskTreatment,
    MitigationStrategy, ActionStatus, Priority
)
from app.models.user import User
from app.schemas.mitigation import (
    MitigationPlanCreate, MitigationPlanUpdate, MitigationActionCreate, 
    MitigationActionUpdate, ActionUpdateCreate, PlanReviewCreate, RiskTreatmentCreate
)


# Mitigation Plan CRUD
def get_mitigation_plan(db: Session, plan_id: int) -> Optional[MitigationPlan]:
    """الحصول على خطة تخفيف بالمعرف"""
    return db.query(MitigationPlan).options(
        joinedload(MitigationPlan.risk),
        joinedload(MitigationPlan.owner),
        joinedload(MitigationPlan.sponsor),
        joinedload(MitigationPlan.created_by),
        joinedload(MitigationPlan.approved_by),
        joinedload(MitigationPlan.actions)
    ).filter(MitigationPlan.id == plan_id).first()


def get_mitigation_plans(
    db: Session, 
    risk_id: Optional[int] = None,
    owner_id: Optional[int] = None,
    status: Optional[str] = None,
    current_user: User = None,
    skip: int = 0,
    limit: int = 100
) -> List[MitigationPlan]:
    """الحصول على قائمة خطط التخفيف"""
    query = db.query(MitigationPlan).options(
        joinedload(MitigationPlan.risk),
        joinedload(MitigationPlan.owner),
        joinedload(MitigationPlan.sponsor)
    )
    
    if risk_id:
        query = query.filter(MitigationPlan.risk_id == risk_id)
    
    if owner_id:
        query = query.filter(MitigationPlan.owner_id == owner_id)
    
    if status:
        query = query.filter(MitigationPlan.status == status)
    
    # فلترة حسب الصلاحيات
    if current_user and current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(MitigationPlan.owner_id == current_user.id)
    
    return query.order_by(desc(MitigationPlan.created_at)).offset(skip).limit(limit).all()


def create_mitigation_plan(db: Session, plan: MitigationPlanCreate, current_user: User) -> MitigationPlan:
    """إنشاء خطة تخفيف جديدة"""
    db_plan = MitigationPlan(
        risk_id=plan.risk_id,
        title=plan.title,
        description=plan.description,
        strategy=plan.strategy,
        objective=plan.objective,
        success_criteria=plan.success_criteria,
        target_risk_reduction=plan.target_risk_reduction,
        estimated_cost=plan.estimated_cost,
        resources_required=plan.resources_required,
        start_date=plan.start_date,
        target_completion_date=plan.target_completion_date,
        owner_id=plan.owner_id,
        sponsor_id=plan.sponsor_id,
        created_by_id=current_user.id
    )
    
    db.add(db_plan)
    db.commit()
    db.refresh(db_plan)
    return db_plan


def update_mitigation_plan(
    db: Session, 
    plan_id: int, 
    plan_update: MitigationPlanUpdate, 
    current_user: User
) -> Optional[MitigationPlan]:
    """تحديث خطة تخفيف"""
    db_plan = get_mitigation_plan(db, plan_id)
    if not db_plan:
        return None
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"] and db_plan.owner_id != current_user.id:
        return None
    
    update_data = plan_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_plan, field, value)
    
    # إعادة حساب التقدم
    db_plan.calculate_progress()
    
    db.commit()
    db.refresh(db_plan)
    return db_plan


def delete_mitigation_plan(db: Session, plan_id: int, current_user: User) -> bool:
    """حذف خطة تخفيف"""
    db_plan = get_mitigation_plan(db, plan_id)
    if not db_plan:
        return False
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        return False
    
    db.delete(db_plan)
    db.commit()
    return True


# Mitigation Action CRUD
def get_mitigation_action(db: Session, action_id: int) -> Optional[MitigationAction]:
    """الحصول على إجراء تخفيف بالمعرف"""
    return db.query(MitigationAction).options(
        joinedload(MitigationAction.plan),
        joinedload(MitigationAction.assigned_to),
        joinedload(MitigationAction.reviewer),
        joinedload(MitigationAction.updates)
    ).filter(MitigationAction.id == action_id).first()


def get_mitigation_actions(
    db: Session,
    plan_id: Optional[int] = None,
    assigned_to_id: Optional[int] = None,
    status: Optional[ActionStatus] = None,
    priority: Optional[Priority] = None,
    overdue_only: bool = False,
    current_user: User = None,
    skip: int = 0,
    limit: int = 100
) -> List[MitigationAction]:
    """الحصول على قائمة إجراءات التخفيف"""
    query = db.query(MitigationAction).options(
        joinedload(MitigationAction.plan),
        joinedload(MitigationAction.assigned_to),
        joinedload(MitigationAction.reviewer)
    )
    
    if plan_id:
        query = query.filter(MitigationAction.plan_id == plan_id)
    
    if assigned_to_id:
        query = query.filter(MitigationAction.assigned_to_id == assigned_to_id)
    
    if status:
        query = query.filter(MitigationAction.status == status)
    
    if priority:
        query = query.filter(MitigationAction.priority == priority)
    
    if overdue_only:
        query = query.filter(
            and_(
                MitigationAction.due_date < datetime.utcnow(),
                MitigationAction.status.notin_([ActionStatus.COMPLETED, ActionStatus.CANCELLED])
            )
        )
    
    # فلترة حسب الصلاحيات
    if current_user and current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(MitigationAction.assigned_to_id == current_user.id)
    
    return query.order_by(asc(MitigationAction.due_date)).offset(skip).limit(limit).all()


def create_mitigation_action(db: Session, action: MitigationActionCreate, current_user: User) -> MitigationAction:
    """إنشاء إجراء تخفيف جديد"""
    db_action = MitigationAction(
        plan_id=action.plan_id,
        title=action.title,
        description=action.description,
        action_type=action.action_type,
        priority=action.priority,
        start_date=action.start_date,
        due_date=action.due_date,
        estimated_effort_hours=action.estimated_effort_hours,
        cost=action.cost,
        assigned_to_id=action.assigned_to_id,
        reviewer_id=action.reviewer_id
    )
    
    db.add(db_action)
    db.commit()
    db.refresh(db_action)
    
    # تحديث تقدم الخطة
    update_plan_progress(db, action.plan_id)
    
    return db_action


def update_mitigation_action(
    db: Session, 
    action_id: int, 
    action_update: MitigationActionUpdate, 
    current_user: User
) -> Optional[MitigationAction]:
    """تحديث إجراء تخفيف"""
    db_action = get_mitigation_action(db, action_id)
    if not db_action:
        return None
    
    # التحقق من الصلاحيات
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        db_action.assigned_to_id != current_user.id):
        return None
    
    update_data = action_update.dict(exclude_unset=True)
    
    # تحديث تاريخ الإنجاز إذا تم تغيير الحالة إلى مكتمل
    if update_data.get('status') == ActionStatus.COMPLETED and not db_action.completion_date:
        update_data['completion_date'] = datetime.utcnow()
    
    for field, value in update_data.items():
        setattr(db_action, field, value)
    
    db.commit()
    db.refresh(db_action)
    
    # تحديث تقدم الخطة
    update_plan_progress(db, db_action.plan_id)
    
    return db_action


def update_plan_progress(db: Session, plan_id: int):
    """تحديث تقدم خطة التخفيف"""
    plan = db.query(MitigationPlan).filter(MitigationPlan.id == plan_id).first()
    if plan:
        plan.calculate_progress()
        db.commit()


# Action Update CRUD
def create_action_update(db: Session, update: ActionUpdateCreate, current_user: User) -> ActionUpdate:
    """إنشاء تحديث للإجراء"""
    db_update = ActionUpdate(
        action_id=update.action_id,
        update_text=update.update_text,
        progress_percentage=update.progress_percentage,
        attachments=update.attachments,
        updated_by_id=current_user.id
    )
    
    db.add(db_update)
    db.commit()
    db.refresh(db_update)
    return db_update


def get_action_updates(db: Session, action_id: int) -> List[ActionUpdate]:
    """الحصول على تحديثات الإجراء"""
    return db.query(ActionUpdate).options(
        joinedload(ActionUpdate.updated_by)
    ).filter(ActionUpdate.action_id == action_id).order_by(desc(ActionUpdate.created_at)).all()


# Plan Review CRUD
def create_plan_review(db: Session, review: PlanReviewCreate, current_user: User) -> PlanReview:
    """إنشاء مراجعة للخطة"""
    db_review = PlanReview(
        plan_id=review.plan_id,
        review_date=review.review_date,
        effectiveness_rating=review.effectiveness_rating,
        progress_assessment=review.progress_assessment,
        issues_identified=review.issues_identified,
        recommendations=review.recommendations,
        continue_plan=review.continue_plan,
        modify_plan=review.modify_plan,
        escalate=review.escalate,
        next_review_date=review.next_review_date,
        reviewer_id=current_user.id
    )
    
    db.add(db_review)
    db.commit()
    db.refresh(db_review)
    
    # تحديث تاريخ المراجعة في الخطة
    plan = db.query(MitigationPlan).filter(MitigationPlan.id == review.plan_id).first()
    if plan:
        plan.last_review_date = review.review_date
        plan.next_review_date = review.next_review_date
        db.commit()
    
    return db_review


def get_plan_reviews(db: Session, plan_id: int) -> List[PlanReview]:
    """الحصول على مراجعات الخطة"""
    return db.query(PlanReview).options(
        joinedload(PlanReview.reviewer)
    ).filter(PlanReview.plan_id == plan_id).order_by(desc(PlanReview.review_date)).all()


# Risk Treatment CRUD
def create_risk_treatment(db: Session, treatment: RiskTreatmentCreate, current_user: User) -> RiskTreatment:
    """إنشاء معالجة للمخاطر"""
    db_treatment = RiskTreatment(
        risk_id=treatment.risk_id,
        treatment_strategy=treatment.treatment_strategy,
        justification=treatment.justification,
        residual_probability=treatment.residual_probability,
        residual_impact=treatment.residual_impact,
        implementation_date=treatment.implementation_date,
        review_date=treatment.review_date
    )
    
    # حساب النتيجة المتبقية
    if treatment.residual_probability and treatment.residual_impact:
        db_treatment.residual_risk_score = treatment.residual_probability * treatment.residual_impact
    
    db.add(db_treatment)
    db.commit()
    db.refresh(db_treatment)
    return db_treatment


# Summary and Statistics
def get_mitigation_summary(db: Session, current_user: User) -> Dict[str, Any]:
    """الحصول على ملخص خطط التخفيف"""
    query = db.query(MitigationPlan)
    
    # فلترة حسب الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(MitigationPlan.owner_id == current_user.id)
    
    total_plans = query.count()
    
    # حسب الاستراتيجية
    by_strategy = {}
    for strategy in MitigationStrategy:
        count = query.filter(MitigationPlan.strategy == strategy).count()
        by_strategy[strategy.value] = count
    
    # حسب الحالة
    by_status = {}
    statuses = ["draft", "approved", "active", "completed", "cancelled"]
    for status in statuses:
        count = query.filter(MitigationPlan.status == status).count()
        by_status[status] = count
    
    # متوسط التقدم
    avg_progress = db.query(func.avg(MitigationPlan.progress_percentage)).scalar() or 0
    
    # الإجراءات المتأخرة
    overdue_actions = db.query(MitigationAction).filter(
        and_(
            MitigationAction.due_date < datetime.utcnow(),
            MitigationAction.status.notin_([ActionStatus.COMPLETED, ActionStatus.CANCELLED])
        )
    ).count()
    
    # الخطط المكتملة
    completed_plans = query.filter(MitigationPlan.status == "completed").count()
    
    return {
        "total_plans": total_plans,
        "by_strategy": by_strategy,
        "by_status": by_status,
        "average_progress": round(avg_progress, 2),
        "overdue_actions": overdue_actions,
        "completed_plans": completed_plans
    }


def get_actions_summary(db: Session, current_user: User) -> Dict[str, Any]:
    """الحصول على ملخص الإجراءات"""
    query = db.query(MitigationAction)
    
    # فلترة حسب الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(MitigationAction.assigned_to_id == current_user.id)
    
    total_actions = query.count()
    
    # حسب الحالة
    by_status = {}
    for status in ActionStatus:
        count = query.filter(MitigationAction.status == status).count()
        by_status[status.value] = count
    
    # حسب الأولوية
    by_priority = {}
    for priority in Priority:
        count = query.filter(MitigationAction.priority == priority).count()
        by_priority[priority.value] = count
    
    # المتأخرة
    overdue_count = query.filter(
        and_(
            MitigationAction.due_date < datetime.utcnow(),
            MitigationAction.status.notin_([ActionStatus.COMPLETED, ActionStatus.CANCELLED])
        )
    ).count()
    
    # المكتملة هذا الشهر
    start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    completed_this_month = query.filter(
        and_(
            MitigationAction.completion_date >= start_of_month,
            MitigationAction.status == ActionStatus.COMPLETED
        )
    ).count()
    
    return {
        "total_actions": total_actions,
        "by_status": by_status,
        "by_priority": by_priority,
        "overdue_count": overdue_count,
        "completed_this_month": completed_this_month
    }
