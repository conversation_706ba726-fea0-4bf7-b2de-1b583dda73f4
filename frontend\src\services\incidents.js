import { apiService } from './api'

// خدمة الحوادث السيبرانية
export const incidentsService = {
  // الحصول على قائمة الحوادث
  getIncidents: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams()
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          queryParams.append(key, params[key])
        }
      })

      const url = `/incidents${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      const response = await apiService.get(url)
      return response
    } catch (error) {
      throw error
    }
  },

  // الحصول على حادث محدد
  getIncident: async (id) => {
    try {
      const response = await apiService.get(`/incidents/${id}`)
      return response
    } catch (error) {
      throw error
    }
  },

  // إنشاء حادث جديد
  createIncident: async (incidentData) => {
    try {
      const response = await apiService.post('/incidents', incidentData)
      return response
    } catch (error) {
      throw error
    }
  },

  // تحديث حادث
  updateIncident: async (id, incidentData) => {
    try {
      const response = await apiService.put(`/incidents/${id}`, incidentData)
      return response
    } catch (error) {
      throw error
    }
  },

  // حذف حادث
  deleteIncident: async (id) => {
    try {
      const response = await apiService.delete(`/incidents/${id}`)
      return response
    } catch (error) {
      throw error
    }
  },

  // الحصول على إحصائيات الحوادث
  getIncidentStats: async () => {
    try {
      const response = await apiService.get('/incidents/stats')
      return response
    } catch (error) {
      throw error
    }
  },

  // الحصول على ملخص الحوادث الأخيرة
  getRecentIncidents: async (limit = 10) => {
    try {
      const response = await apiService.get(`/incidents/summary/recent?limit=${limit}`)
      return response
    } catch (error) {
      throw error
    }
  },

  // البحث في الحوادث
  searchIncidents: async (searchTerm, filters = {}) => {
    try {
      const params = {
        search: searchTerm,
        ...filters
      }
      return await incidentsService.getIncidents(params)
    } catch (error) {
      throw error
    }
  },

  // تصدير الحوادث (للتطوير المستقبلي)
  exportIncidents: async (format = 'csv', filters = {}) => {
    try {
      const params = new URLSearchParams({
        format,
        ...filters
      })
      
      const response = await apiService.get(`/incidents/export?${params.toString()}`, {
        responseType: 'blob'
      })
      
      return response
    } catch (error) {
      throw error
    }
  },
}
