"""
التطبيق الرئيسي لنظام إدارة الحوكمة والمخاطر والامتثال
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError
from .config import settings
from .database import create_tables, engine, SessionLocal
from .models.user import User, UserRole
from .core.security import get_password_hash
from .api import auth, users, incidents

# إنشاء التطبيق
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="نظام متكامل لإدارة الحوكمة والمخاطر والامتثال مع التركيز على الامتثال للمعايير السعودية",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# تسجيل المسارات
app.include_router(auth.router, prefix="/api/auth", tags=["المصادقة"])
app.include_router(users.router, prefix="/api/users", tags=["المستخدمون"])
app.include_router(incidents.router, prefix="/api/incidents", tags=["الحوادث السيبرانية"])


@app.exception_handler(SQLAlchemyError)
async def sqlalchemy_exception_handler(request, exc):
    """معالج أخطاء قاعدة البيانات"""
    return JSONResponse(
        status_code=500,
        content={"detail": "خطأ في قاعدة البيانات"}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """معالج الأخطاء العام"""
    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={"detail": f"خطأ داخلي: {str(exc)}"}
        )
    return JSONResponse(
        status_code=500,
        content={"detail": "خطأ داخلي في الخادم"}
    )


@app.on_event("startup")
async def startup_event():
    """أحداث بدء التشغيل"""
    # إنشاء جداول قاعدة البيانات
    create_tables()
    
    # إنشاء مستخدم مدير افتراضي إذا لم يكن موجوداً
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="مدير النظام",
                hashed_password=get_password_hash("Admin@123"),
                role=UserRole.ADMIN,
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            print("تم إنشاء مستخدم مدير افتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: Admin@123")
    except Exception as e:
        print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
    finally:
        db.close()


@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "مرحباً بك في نظام إدارة الحوكمة والمخاطر والامتثال",
        "version": settings.app_version,
        "docs": "/api/docs"
    }


@app.get("/api/health")
async def health_check():
    """فحص صحة النظام"""
    try:
        # فحص الاتصال بقاعدة البيانات
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        
        return {
            "status": "healthy",
            "database": "connected",
            "version": settings.app_version
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e)
            }
        )
