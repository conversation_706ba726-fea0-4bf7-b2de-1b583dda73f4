import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
  ExclamationTriangleIcon,
  PlusIcon,
  ChartBarIcon,
  UsersIcon,
  ShieldExclamationIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import { ROUTES } from '../../utils/constants'
import { PERMISSIONS, hasPermission } from '../../utils/permissions'
import PermissionGuard from '../auth/PermissionGuard'
import UserCreationTest from '../debug/UserCreationTest'
import AuthDebug from '../debug/AuthDebug'

const ProfessionalDashboard = ({ user }) => {
  const [stats, setStats] = useState({
    incidents: { total: 0, open: 0, resolved: 0, critical: 0 },
    risks: { total: 0, high: 0, medium: 0, low: 0 },
    users: { total: 0, active: 0, inactive: 0 },
    compliance: { total: 0, compliant: 0, nonCompliant: 0 }
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      // بيانات وهمية للعرض التوضيحي
      const mockStats = {
        incidents: { total: 24, open: 8, resolved: 16, critical: 3 },
        risks: { total: 45, high: 12, medium: 23, low: 10 },
        users: { total: 156, active: 142, inactive: 14 },
        compliance: { total: 28, compliant: 24, nonCompliant: 4 }
      }
      
      setTimeout(() => {
        setStats(mockStats)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error)
      setIsLoading(false)
    }
  }

  const StatCard = ({ title, value, subtitle, icon: Icon, color, trend, trendValue }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${
              trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend === 'up' ? (
                <ArrowTrendingUpIcon className="w-4 h-4 ml-1" />
              ) : (
                <ArrowTrendingDownIcon className="w-4 h-4 ml-1" />
              )}
              <span>{trendValue}</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-8 h-8 text-white" />
        </div>
      </div>
    </div>
  )

  const QuickActionCard = ({ title, description, icon: Icon, href, color, permission }) => (
    <PermissionGuard userRole={user?.role} permission={permission}>
      <Link
        to={href}
        className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:scale-105 transition-all duration-300"
      >
        <div className="flex items-center">
          <div className={`p-3 rounded-full ${color} group-hover:scale-110 transition-transform duration-300`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          <div className="mr-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {description}
            </p>
          </div>
        </div>
      </Link>
    </PermissionGuard>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* ترحيب */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              مرحباً، {user?.full_name || user?.username}
            </h1>
            <p className="text-blue-100 text-lg">
              نظرة عامة على حالة النظام والأنشطة الحديثة
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <ChartBarIcon className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="إجمالي الحوادث"
          value={stats.incidents.total}
          subtitle={`${stats.incidents.open} مفتوح، ${stats.incidents.resolved} محلول`}
          icon={ExclamationTriangleIcon}
          color="bg-red-500"
          trend="down"
          trendValue="12% من الشهر الماضي"
        />
        
        <StatCard
          title="إجمالي المخاطر"
          value={stats.risks.total}
          subtitle={`${stats.risks.high} عالية، ${stats.risks.medium} متوسطة`}
          icon={ShieldExclamationIcon}
          color="bg-orange-500"
          trend="up"
          trendValue="8% من الشهر الماضي"
        />
        
        <PermissionGuard userRole={user?.role} permission={PERMISSIONS.USERS_VIEW}>
          <StatCard
            title="المستخدمون"
            value={stats.users.total}
            subtitle={`${stats.users.active} نشط، ${stats.users.inactive} غير نشط`}
            icon={UsersIcon}
            color="bg-blue-500"
            trend="up"
            trendValue="5% من الشهر الماضي"
          />
        </PermissionGuard>
        
        <StatCard
          title="حالة الامتثال"
          value={`${Math.round((stats.compliance.compliant / stats.compliance.total) * 100)}%`}
          subtitle={`${stats.compliance.compliant} من ${stats.compliance.total} متطلب`}
          icon={DocumentTextIcon}
          color="bg-green-500"
          trend="up"
          trendValue="3% من الشهر الماضي"
        />
      </div>

      {/* إجراءات سريعة */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          إجراءات سريعة
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <QuickActionCard
            title="تسجيل حادث جديد"
            description="إبلاغ عن حادث سيبراني أو أمني"
            icon={PlusIcon}
            href={ROUTES.INCIDENT_CREATE}
            color="bg-red-500"
            permission={PERMISSIONS.INCIDENTS_CREATE}
          />
          
          <QuickActionCard
            title="عرض جميع الحوادث"
            description="مراجعة ومتابعة الحوادث المسجلة"
            icon={EyeIcon}
            href={ROUTES.INCIDENTS}
            color="bg-orange-500"
            permission={PERMISSIONS.INCIDENTS_VIEW}
          />
          
          <QuickActionCard
            title="إدارة المستخدمين"
            description="إضافة وإدارة مستخدمي النظام"
            icon={UsersIcon}
            href={ROUTES.USERS}
            color="bg-blue-500"
            permission={PERMISSIONS.USERS_VIEW}
          />
          
          <QuickActionCard
            title="تقييم المخاطر"
            description="إضافة وتقييم المخاطر الجديدة"
            icon={ShieldExclamationIcon}
            href={ROUTES.RISKS}
            color="bg-purple-500"
            permission={PERMISSIONS.RISKS_VIEW}
          />
          
          <QuickActionCard
            title="التقارير والتحليلات"
            description="عرض التقارير والإحصائيات"
            icon={ChartBarIcon}
            href={ROUTES.REPORTS_DASHBOARD}
            color="bg-green-500"
            permission={PERMISSIONS.REPORTS_VIEW}
          />
          
          <QuickActionCard
            title="إدارة السياسات"
            description="مراجعة وتحديث السياسات"
            icon={DocumentTextIcon}
            href={ROUTES.POLICIES}
            color="bg-indigo-500"
            permission={PERMISSIONS.POLICIES_VIEW}
          />
        </div>
      </div>

      {/* نشاط حديث */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* الحوادث الحديثة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              الحوادث الحديثة
            </h3>
            <Link
              to={ROUTES.INCIDENTS}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              عرض الكل
            </Link>
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full ml-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    محاولة اختراق مشبوهة
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    منذ {item} ساعات
                  </p>
                </div>
                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                  عالي
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* المخاطر الحديثة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              المخاطر المحدثة
            </h3>
            <Link
              to={ROUTES.RISKS}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              عرض الكل
            </Link>
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-orange-500 rounded-full ml-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    مخاطر أمان البيانات
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    تم التحديث منذ {item} أيام
                  </p>
                </div>
                <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                  متوسط
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* مكونات التشخيص - للمديرين فقط */}
      {user?.role === 'ADMIN' && (
        <>
          <AuthDebug />
          <UserCreationTest />
        </>
      )}
    </div>
  )
}

export default ProfessionalDashboard
