import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  ExclamationTriangleIcon,
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { incidentsService } from '../services/incidents'
import { ROUTES, SEVERITY_COLORS, STATUS_COLORS } from '../utils/constants'
import { formatDate, getSeverityText, getStatusText, getIncidentTypeText } from '../utils/helpers'
import LoadingSpinner from '../components/common/LoadingSpinner'

const Dashboard = ({ user }) => {
  const [stats, setStats] = useState(null)
  const [recentIncidents, setRecentIncidents] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      // بيانات وهمية مؤقتة حتى يتم إصلاح API الحوادث
      const statsData = {
        total_incidents: 0,
        open_incidents: 0,
        resolved_incidents: 0,
        critical_incidents: 0,
        incidents_by_type: {},
        incidents_by_month: {}
      }
      const recentData = []

      setStats(statsData)
      setRecentIncidents(recentData)
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  const statCards = [
    {
      title: 'إجمالي الحوادث',
      value: stats?.total_incidents || 0,
      icon: ExclamationTriangleIcon,
      color: 'bg-blue-600',
      textColor: 'text-blue-100'
    },
    {
      title: 'الحوادث المفتوحة',
      value: stats?.open_incidents || 0,
      icon: ClockIcon,
      color: 'bg-yellow-600',
      textColor: 'text-yellow-100'
    },
    {
      title: 'الحوادث المحلولة',
      value: stats?.resolved_incidents || 0,
      icon: CheckCircleIcon,
      color: 'bg-green-600',
      textColor: 'text-green-100'
    },
    {
      title: 'الحوادث الحرجة',
      value: stats?.critical_incidents || 0,
      icon: XCircleIcon,
      color: 'bg-red-600',
      textColor: 'text-red-100'
    }
  ]

  return (
    <div className="space-y-6">
      {/* ترحيب */}
      <div className="bg-gradient-to-l from-primary-600 to-primary-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          مرحباً، {user?.full_name || user?.username}
        </h1>
        <p className="text-primary-100">
          مرحباً بك في نظام إدارة الحوكمة والمخاطر والامتثال
        </p>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${card.color}`}>
                  <card.icon className={`h-6 w-6 ${card.textColor}`} />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-dark-400">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الحوادث الأخيرة */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-white">الحوادث الأخيرة</h2>
              <Link
                to={ROUTES.INCIDENTS}
                className="text-primary-400 hover:text-primary-300 text-sm"
              >
                عرض الكل
              </Link>
            </div>
          </div>
          <div className="card-body">
            {recentIncidents.length > 0 ? (
              <div className="space-y-4">
                {recentIncidents.map((incident) => (
                  <div
                    key={incident.id}
                    className="flex items-center justify-between p-3 bg-dark-700 rounded-lg"
                  >
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-white mb-1">
                        {incident.title}
                      </h3>
                      <div className="flex items-center space-x-4 space-x-reverse text-xs text-dark-400">
                        <span>{getIncidentTypeText(incident.incident_type)}</span>
                        <span>{formatDate(incident.incident_date)}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className={`badge ${getSeverityColor(incident.severity)}`}>
                        {getSeverityText(incident.severity)}
                      </span>
                      <span className={`badge ${getStatusColor(incident.status)}`}>
                        {getStatusText(incident.status)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-dark-500" />
                <p className="mt-2 text-sm text-dark-400">لا توجد حوادث مسجلة</p>
              </div>
            )}
          </div>
        </div>

        {/* إحصائيات حسب النوع */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-white">الحوادث حسب النوع</h2>
          </div>
          <div className="card-body">
            {stats?.incidents_by_type && Object.keys(stats.incidents_by_type).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(stats.incidents_by_type).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm text-dark-300">
                      {getIncidentTypeText(type)}
                    </span>
                    <span className="text-sm font-medium text-white">{count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <ChartBarIcon className="mx-auto h-12 w-12 text-dark-500" />
                <p className="mt-2 text-sm text-dark-400">لا توجد إحصائيات متاحة</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* إجراءات سريعة */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-white">إجراءات سريعة</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to={ROUTES.INCIDENT_CREATE}
              className="flex items-center p-4 bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors"
            >
              <PlusIcon className="h-6 w-6 text-white ml-3" />
              <span className="text-white font-medium">تسجيل حادث جديد</span>
            </Link>
            
            <Link
              to={ROUTES.INCIDENTS}
              className="flex items-center p-4 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors"
            >
              <ExclamationTriangleIcon className="h-6 w-6 text-white ml-3" />
              <span className="text-white font-medium">عرض جميع الحوادث</span>
            </Link>
            
            <button className="flex items-center p-4 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
              <ChartBarIcon className="h-6 w-6 text-white ml-3" />
              <span className="text-white font-medium">تصدير التقارير</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// وظائف مساعدة للألوان
const getSeverityColor = (severity) => {
  return SEVERITY_COLORS[severity] || SEVERITY_COLORS.medium
}

const getStatusColor = (status) => {
  return STATUS_COLORS[status] || STATUS_COLORS.open
}

export default Dashboard
