import { format, formatDistanceToNow } from 'date-fns'
import { ar } from 'date-fns/locale'
import { 
  INCIDENT_TYPES, 
  SEVERITY_LEVELS, 
  INCIDENT_STATUS, 
  SEVERITY_COLORS, 
  STATUS_COLORS 
} from './constants'

/**
 * تنسيق التاريخ
 */
export const formatDate = (date, formatStr = 'dd/MM/yyyy') => {
  if (!date) return ''
  return format(new Date(date), formatStr, { locale: ar })
}

/**
 * تنسيق التاريخ والوقت
 */
export const formatDateTime = (date) => {
  if (!date) return ''
  return format(new Date(date), 'dd/MM/yyyy HH:mm', { locale: ar })
}

/**
 * حساب المدة منذ التاريخ
 */
export const timeAgo = (date) => {
  if (!date) return ''
  return formatDistanceToNow(new Date(date), { 
    addSuffix: true, 
    locale: ar 
  })
}

/**
 * الحصول على نص نوع الحادث
 */
export const getIncidentTypeText = (type) => {
  return INCIDENT_TYPES[type] || type
}

/**
 * الحصول على نص مستوى الخطورة
 */
export const getSeverityText = (severity) => {
  return SEVERITY_LEVELS[severity] || severity
}

/**
 * الحصول على نص حالة الحادث
 */
export const getStatusText = (status) => {
  return INCIDENT_STATUS[status] || status
}

/**
 * الحصول على ألوان مستوى الخطورة
 */
export const getSeverityColor = (severity) => {
  return SEVERITY_COLORS[severity] || SEVERITY_COLORS.medium
}

/**
 * الحصول على ألوان حالة الحادث
 */
export const getStatusColor = (status) => {
  return STATUS_COLORS[status] || STATUS_COLORS.open
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * التحقق من قوة كلمة المرور
 */
export const isStrongPassword = (password) => {
  const minLength = password.length >= 8
  const hasUpper = /[A-Z]/.test(password)
  const hasLower = /[a-z]/.test(password)
  const hasNumber = /\d/.test(password)
  const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  
  return minLength && hasUpper && hasLower && hasNumber && hasSpecial
}

/**
 * تنظيف النص من الأحرف الخطيرة
 */
export const sanitizeText = (text) => {
  if (!text) return ''
  return text.replace(/[<>]/g, '')
}

/**
 * تقصير النص
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/**
 * تحويل الحجم بالبايت إلى نص قابل للقراءة
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 بايت'
  
  const k = 1024
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * إنشاء معرف فريد
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * نسخ النص إلى الحافظة
 */
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    console.error('فشل في نسخ النص:', err)
    return false
  }
}

/**
 * تحميل ملف
 */
export const downloadFile = (data, filename, type = 'text/plain') => {
  const blob = new Blob([data], { type })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * التحقق من دعم المتصفح لميزة معينة
 */
export const isFeatureSupported = (feature) => {
  switch (feature) {
    case 'clipboard':
      return navigator.clipboard && navigator.clipboard.writeText
    case 'notifications':
      return 'Notification' in window
    case 'serviceWorker':
      return 'serviceWorker' in navigator
    default:
      return false
  }
}
