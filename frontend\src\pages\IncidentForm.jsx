import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { ArrowRightIcon } from '@heroicons/react/24/outline'
import { incidentsService } from '../services/incidents'
import { ROUTES, INCIDENT_TYPES, SEVERITY_LEVELS, INCIDENT_STATUS, VALIDATION_MESSAGES } from '../utils/constants'
import LoadingSpinner from '../components/common/LoadingSpinner'

const IncidentForm = ({ user, isEdit = false }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(isEdit)
  const navigate = useNavigate()
  const { id } = useParams()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm({
    defaultValues: {
      incident_date: new Date().toISOString().split('T')[0],
      severity: 'medium',
      nca_reported: 'لا'
    }
  })

  useEffect(() => {
    if (isEdit && id) {
      loadIncidentData()
    }
  }, [isEdit, id])

  const loadIncidentData = async () => {
    try {
      setIsLoadingData(true)
      const incident = await incidentsService.getIncident(id)
      
      // تعبئة النموذج بالبيانات الموجودة
      Object.keys(incident).forEach(key => {
        if (key === 'incident_date') {
          setValue(key, new Date(incident[key]).toISOString().split('T')[0])
        } else {
          setValue(key, incident[key])
        }
      })
    } catch (error) {
      console.error('خطأ في تحميل بيانات الحادث:', error)
      toast.error('فشل في تحميل بيانات الحادث')
      navigate(ROUTES.INCIDENTS)
    } finally {
      setIsLoadingData(false)
    }
  }

  const onSubmit = async (data) => {
    setIsLoading(true)
    try {
      // تحويل التاريخ إلى ISO format
      const formattedData = {
        ...data,
        incident_date: new Date(data.incident_date).toISOString()
      }

      if (isEdit) {
        await incidentsService.updateIncident(id, formattedData)
        toast.success('تم تحديث الحادث بنجاح')
      } else {
        await incidentsService.createIncident(formattedData)
        toast.success('تم تسجيل الحادث بنجاح')
      }
      
      navigate(ROUTES.INCIDENTS)
    } catch (error) {
      console.error('خطأ في حفظ الحادث:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* العنوان */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 space-x-reverse mb-4">
          <button
            onClick={() => navigate(ROUTES.INCIDENTS)}
            className="text-dark-400 hover:text-white"
          >
            <ArrowRightIcon className="h-5 w-5" />
          </button>
          <h1 className="text-2xl font-bold text-white">
            {isEdit ? 'تعديل الحادث' : 'تسجيل حادث جديد'}
          </h1>
        </div>
        <p className="text-dark-400">
          {isEdit ? 'قم بتعديل تفاصيل الحادث السيبراني' : 'قم بتسجيل حادث سيبراني جديد في النظام'}
        </p>
      </div>

      {/* النموذج */}
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* عنوان الحادث */}
              <div className="lg:col-span-2">
                <label htmlFor="title" className="form-label">
                  عنوان الحادث *
                </label>
                <input
                  id="title"
                  type="text"
                  className={`input-field ${errors.title ? 'border-red-500' : ''}`}
                  placeholder="أدخل عنوان الحادث"
                  {...register('title', {
                    required: VALIDATION_MESSAGES.required,
                    minLength: {
                      value: 5,
                      message: VALIDATION_MESSAGES.minLength(5)
                    }
                  })}
                />
                {errors.title && (
                  <p className="form-error">{errors.title.message}</p>
                )}
              </div>

              {/* نوع الحادث */}
              <div>
                <label htmlFor="incident_type" className="form-label">
                  نوع الحادث *
                </label>
                <select
                  id="incident_type"
                  className={`input-field ${errors.incident_type ? 'border-red-500' : ''}`}
                  {...register('incident_type', {
                    required: VALIDATION_MESSAGES.required
                  })}
                >
                  <option value="">اختر نوع الحادث</option>
                  {Object.entries(INCIDENT_TYPES).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
                {errors.incident_type && (
                  <p className="form-error">{errors.incident_type.message}</p>
                )}
              </div>

              {/* مستوى الخطورة */}
              <div>
                <label htmlFor="severity" className="form-label">
                  مستوى الخطورة *
                </label>
                <select
                  id="severity"
                  className={`input-field ${errors.severity ? 'border-red-500' : ''}`}
                  {...register('severity', {
                    required: VALIDATION_MESSAGES.required
                  })}
                >
                  {Object.entries(SEVERITY_LEVELS).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
                {errors.severity && (
                  <p className="form-error">{errors.severity.message}</p>
                )}
              </div>

              {/* تاريخ الحادث */}
              <div>
                <label htmlFor="incident_date" className="form-label">
                  تاريخ الحادث *
                </label>
                <input
                  id="incident_date"
                  type="date"
                  className={`input-field ${errors.incident_date ? 'border-red-500' : ''}`}
                  {...register('incident_date', {
                    required: VALIDATION_MESSAGES.required
                  })}
                />
                {errors.incident_date && (
                  <p className="form-error">{errors.incident_date.message}</p>
                )}
              </div>

              {/* الإبلاغ لهيئة الاتصالات */}
              <div>
                <label htmlFor="nca_reported" className="form-label">
                  هل تم الإبلاغ لهيئة الاتصالات؟
                </label>
                <select
                  id="nca_reported"
                  className="input-field"
                  {...register('nca_reported')}
                >
                  <option value="لا">لا</option>
                  <option value="نعم">نعم</option>
                  <option value="قيد المراجعة">قيد المراجعة</option>
                </select>
              </div>
            </div>

            {/* الوصف */}
            <div>
              <label htmlFor="description" className="form-label">
                وصف الحادث
              </label>
              <textarea
                id="description"
                rows={4}
                className="input-field"
                placeholder="أدخل وصف تفصيلي للحادث"
                {...register('description')}
              />
            </div>

            {/* الأنظمة المتأثرة */}
            <div>
              <label htmlFor="affected_systems" className="form-label">
                الأنظمة المتأثرة
              </label>
              <textarea
                id="affected_systems"
                rows={3}
                className="input-field"
                placeholder="اذكر الأنظمة والخدمات المتأثرة"
                {...register('affected_systems')}
              />
            </div>

            {/* وصف التأثير */}
            <div>
              <label htmlFor="impact_description" className="form-label">
                وصف التأثير
              </label>
              <textarea
                id="impact_description"
                rows={3}
                className="input-field"
                placeholder="اوصف تأثير الحادث على العمليات"
                {...register('impact_description')}
              />
            </div>

            {/* الإجراءات المتخذة */}
            <div>
              <label htmlFor="actions_taken" className="form-label">
                الإجراءات المتخذة
              </label>
              <textarea
                id="actions_taken"
                rows={4}
                className="input-field"
                placeholder="اذكر الإجراءات التي تم اتخاذها للتعامل مع الحادث"
                {...register('actions_taken')}
              />
            </div>

            {/* حالة الحادث (للتعديل فقط) */}
            {isEdit && (
              <div>
                <label htmlFor="status" className="form-label">
                  حالة الحادث
                </label>
                <select
                  id="status"
                  className="input-field"
                  {...register('status')}
                >
                  {Object.entries(INCIDENT_STATUS).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>
            )}

            {/* الدروس المستفادة (للتعديل فقط) */}
            {isEdit && (
              <div>
                <label htmlFor="lessons_learned" className="form-label">
                  الدروس المستفادة
                </label>
                <textarea
                  id="lessons_learned"
                  rows={3}
                  className="input-field"
                  placeholder="اذكر الدروس المستفادة من هذا الحادث"
                  {...register('lessons_learned')}
                />
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-dark-700">
              <button
                type="button"
                onClick={() => navigate(ROUTES.INCIDENTS)}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary flex items-center"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="small" className="ml-2" />
                    {isEdit ? 'جاري التحديث...' : 'جاري الحفظ...'}
                  </>
                ) : (
                  isEdit ? 'تحديث الحادث' : 'حفظ الحادث'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default IncidentForm
