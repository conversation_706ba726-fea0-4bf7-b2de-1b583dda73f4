#!/usr/bin/env python3
"""
سكريبت لإنشاء جداول إدارة المخاطر وخطط التخفيف
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import engine, Base
from app.models.risk import Risk, RiskAssessment, RiskControl, RiskIncident
from app.models.mitigation import MitigationPlan, MitigationAction, ActionUpdate, PlanReview, RiskTreatment
from app.models.user import User
from app.models.incident import Incident

def create_risk_tables():
    """إنشاء جداول إدارة المخاطر"""
    try:
        print("🚀 بدء إنشاء جداول إدارة المخاطر...")
        
        # إنشاء جميع الجداول
        Base.metadata.create_all(bind=engine)
        
        print("✅ تم إنشاء جداول إدارة المخاطر بنجاح!")
        print("\nالجداول المنشأة:")
        print("- risks (المخاطر)")
        print("- risk_assessments (تقييمات المخاطر)")
        print("- risk_controls (ضوابط المخاطر)")
        print("- risk_incidents (حوادث المخاطر)")
        print("- mitigation_plans (خطط التخفيف)")
        print("- mitigation_actions (إجراءات التخفيف)")
        print("- action_updates (تحديثات الإجراءات)")
        print("- plan_reviews (مراجعات الخطط)")
        print("- risk_treatments (معالجات المخاطر)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def verify_tables():
    """التحقق من إنشاء الجداول"""
    try:
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        expected_tables = [
            'users', 'incidents', 'risks', 'risk_assessments', 
            'risk_controls', 'risk_incidents', 'mitigation_plans',
            'mitigation_actions', 'action_updates', 'plan_reviews', 
            'risk_treatments'
        ]
        
        print("\n🔍 التحقق من الجداول:")
        for table in expected_tables:
            if table in tables:
                print(f"✅ {table}")
            else:
                print(f"❌ {table} - غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من الجداول: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🏗️  إنشاء جداول نظام إدارة المخاطر")
    print("=" * 50)
    
    # إنشاء الجداول
    if create_risk_tables():
        # التحقق من الجداول
        verify_tables()
        print("\n🎉 تم إنشاء نظام إدارة المخاطر بنجاح!")
    else:
        print("\n💥 فشل في إنشاء نظام إدارة المخاطر!")
        sys.exit(1)
