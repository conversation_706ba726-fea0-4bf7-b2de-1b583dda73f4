@tailwind base;
@tailwind components;
@tailwind utilities;

/* تخصيصات للغة العربية */
@layer base {
  html {
    direction: rtl;
  }
  
  body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    @apply bg-dark-900 text-white;
  }
  
  * {
    @apply border-dark-700;
  }
}

/* مكونات مخصصة */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .input-field {
    @apply w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .card {
    @apply bg-dark-800 border border-dark-700 rounded-lg shadow-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-dark-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }
  
  .badge-info {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
  }
}

/* تحسينات للتمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}

/* تحسينات للنماذج */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-dark-300 mb-2;
}

.form-error {
  @apply text-red-400 text-sm mt-1;
}

/* تحسينات للجداول */
.table {
  @apply w-full text-sm text-right text-dark-300;
}

.table thead {
  @apply text-xs text-dark-400 uppercase bg-dark-800;
}

.table th {
  @apply px-6 py-3;
}

.table td {
  @apply px-6 py-4 border-b border-dark-700;
}

.table tbody tr {
  @apply bg-dark-900 hover:bg-dark-800 transition-colors duration-200;
}

/* تحسينات للتنبيهات */
.alert {
  @apply p-4 mb-4 text-sm rounded-lg;
}

.alert-success {
  @apply text-green-800 bg-green-50 dark:bg-green-900 dark:text-green-300;
}

.alert-error {
  @apply text-red-800 bg-red-50 dark:bg-red-900 dark:text-red-300;
}

.alert-warning {
  @apply text-yellow-800 bg-yellow-50 dark:bg-yellow-900 dark:text-yellow-300;
}

.alert-info {
  @apply text-blue-800 bg-blue-50 dark:bg-blue-900 dark:text-blue-300;
}
