import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import { incidentsService } from '../services/incidents'
import { 
  ROUTES, 
  INCIDENT_TYPES, 
  SEVERITY_LEVELS, 
  INCIDENT_STATUS,
  SEVERITY_COLORS,
  STATUS_COLORS
} from '../utils/constants'
import { 
  formatDate, 
  getSeverityText, 
  getStatusText, 
  getIncidentTypeText,
  truncateText 
} from '../utils/helpers'
import LoadingSpinner from '../components/common/LoadingSpinner'
import toast from 'react-hot-toast'

const IncidentList = ({ user }) => {
  const [incidents, setIncidents] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    incident_type: '',
    severity: '',
    status: ''
  })
  const [showFilters, setShowFilters] = useState(false)
  const [selectedIncident, setSelectedIncident] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    loadIncidents()
  }, [searchTerm, filters])

  const loadIncidents = async () => {
    try {
      setIsLoading(true)
      const params = {
        search: searchTerm || undefined,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      }
      const data = await incidentsService.getIncidents(params)
      setIncidents(data)
    } catch (error) {
      console.error('خطأ في تحميل الحوادث:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
  }

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }))
  }

  const clearFilters = () => {
    setSearchTerm('')
    setFilters({
      incident_type: '',
      severity: '',
      status: ''
    })
  }

  const handleDelete = async (incidentId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الحادث؟')) {
      return
    }

    try {
      await incidentsService.deleteIncident(incidentId)
      toast.success('تم حذف الحادث بنجاح')
      loadIncidents()
    } catch (error) {
      console.error('خطأ في حذف الحادث:', error)
    }
  }

  const getSeverityColor = (severity) => {
    return SEVERITY_COLORS[severity] || SEVERITY_COLORS.medium
  }

  const getStatusColor = (status) => {
    return STATUS_COLORS[status] || STATUS_COLORS.open
  }

  return (
    <div className="space-y-6">
      {/* العنوان والإجراءات */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">الحوادث السيبرانية</h1>
          <p className="text-dark-400 mt-1">إدارة ومتابعة الحوادث السيبرانية</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link
            to={ROUTES.INCIDENT_CREATE}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 ml-2" />
            تسجيل حادث جديد
          </Link>
        </div>
      </div>

      {/* البحث والفلاتر */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* البحث */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-dark-400" />
              <input
                type="text"
                placeholder="البحث في الحوادث..."
                value={searchTerm}
                onChange={handleSearch}
                className="input-field pr-10"
              />
            </div>

            {/* أزرار الفلاتر */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`btn-secondary flex items-center ${showFilters ? 'bg-primary-600' : ''}`}
              >
                <FunnelIcon className="h-5 w-5 ml-2" />
                الفلاتر
              </button>

              {(searchTerm || Object.values(filters).some(v => v !== '')) && (
                <button
                  onClick={clearFilters}
                  className="text-primary-400 hover:text-primary-300 text-sm"
                >
                  مسح الفلاتر
                </button>
              )}
            </div>
          </div>

          {/* الفلاتر */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-dark-700">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-dark-300 mb-2">
                    نوع الحادث
                  </label>
                  <select
                    value={filters.incident_type}
                    onChange={(e) => handleFilterChange('incident_type', e.target.value)}
                    className="input-field"
                  >
                    <option value="">جميع الأنواع</option>
                    {Object.entries(INCIDENT_TYPES).map(([key, value]) => (
                      <option key={key} value={key}>{value}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-dark-300 mb-2">
                    مستوى الخطورة
                  </label>
                  <select
                    value={filters.severity}
                    onChange={(e) => handleFilterChange('severity', e.target.value)}
                    className="input-field"
                  >
                    <option value="">جميع المستويات</option>
                    {Object.entries(SEVERITY_LEVELS).map(([key, value]) => (
                      <option key={key} value={key}>{value}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-dark-300 mb-2">
                    الحالة
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="input-field"
                  >
                    <option value="">جميع الحالات</option>
                    {Object.entries(INCIDENT_STATUS).map(([key, value]) => (
                      <option key={key} value={key}>{value}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* قائمة الحوادث */}
      <div className="card">
        <div className="card-body p-0">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="large" />
            </div>
          ) : incidents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>العنوان</th>
                    <th>النوع</th>
                    <th>الخطورة</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                    <th>المُبلِغ</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {incidents.map((incident) => (
                    <tr key={incident.id}>
                      <td>
                        <div>
                          <div className="font-medium text-white">
                            {truncateText(incident.title, 50)}
                          </div>
                          {incident.reference_number && (
                            <div className="text-xs text-dark-400">
                              {incident.reference_number}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className="text-sm text-dark-300">
                          {getIncidentTypeText(incident.incident_type)}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${getSeverityColor(incident.severity)}`}>
                          {getSeverityText(incident.severity)}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${getStatusColor(incident.status)}`}>
                          {getStatusText(incident.status)}
                        </span>
                      </td>
                      <td>
                        <span className="text-sm text-dark-300">
                          {formatDate(incident.incident_date)}
                        </span>
                      </td>
                      <td>
                        <span className="text-sm text-dark-300">
                          {incident.created_by_full_name || incident.created_by_username}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => setSelectedIncident(incident)}
                            className="text-blue-400 hover:text-blue-300"
                            title="عرض التفاصيل"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>

                          {(user?.role === 'admin' || incident.created_by === user?.id) && (
                            <>
                              <Link
                                to={`/incidents/edit/${incident.id}`}
                                className="text-yellow-400 hover:text-yellow-300"
                                title="تعديل"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>

                              {user?.role === 'admin' && (
                                <button
                                  onClick={() => handleDelete(incident.id)}
                                  className="text-red-400 hover:text-red-300"
                                  title="حذف"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              )}
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 text-dark-500">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-dark-300">لا توجد حوادث</h3>
              <p className="mt-1 text-sm text-dark-500">ابدأ بتسجيل حادث جديد</p>
              <div className="mt-6">
                <Link
                  to={ROUTES.INCIDENT_CREATE}
                  className="btn-primary flex items-center mx-auto w-fit"
                >
                  <PlusIcon className="h-5 w-5 ml-2" />
                  تسجيل حادث جديد
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نافذة عرض تفاصيل الحادث */}
      {selectedIncident && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity bg-dark-900 bg-opacity-75" onClick={() => setSelectedIncident(null)} />

            <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-dark-800 shadow-xl rounded-lg">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-white">تفاصيل الحادث</h3>
                <button
                  onClick={() => setSelectedIncident(null)}
                  className="text-dark-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">العنوان</label>
                    <p className="text-white">{selectedIncident.title}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">الرقم المرجعي</label>
                    <p className="text-white">{selectedIncident.reference_number || 'غير محدد'}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">نوع الحادث</label>
                    <p className="text-white">{getIncidentTypeText(selectedIncident.incident_type)}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">مستوى الخطورة</label>
                    <span className={`badge ${getSeverityColor(selectedIncident.severity)}`}>
                      {getSeverityText(selectedIncident.severity)}
                    </span>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">الحالة</label>
                    <span className={`badge ${getStatusColor(selectedIncident.status)}`}>
                      {getStatusText(selectedIncident.status)}
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">تاريخ الحادث</label>
                    <p className="text-white">{formatDate(selectedIncident.incident_date)}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">تاريخ الإبلاغ</label>
                    <p className="text-white">{formatDate(selectedIncident.reported_date)}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">المُبلِغ</label>
                    <p className="text-white">{selectedIncident.created_by_full_name || selectedIncident.created_by_username}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-1">الإبلاغ لهيئة الاتصالات</label>
                    <p className="text-white">{selectedIncident.nca_reported}</p>
                  </div>

                  {selectedIncident.resolved_date && (
                    <div>
                      <label className="block text-sm font-medium text-dark-300 mb-1">تاريخ الحل</label>
                      <p className="text-white">{formatDate(selectedIncident.resolved_date)}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* الوصف والتفاصيل */}
              <div className="mt-6 space-y-4">
                {selectedIncident.description && (
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-2">وصف الحادث</label>
                    <p className="text-white bg-dark-700 p-3 rounded-lg">{selectedIncident.description}</p>
                  </div>
                )}

                {selectedIncident.affected_systems && (
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-2">الأنظمة المتأثرة</label>
                    <p className="text-white bg-dark-700 p-3 rounded-lg">{selectedIncident.affected_systems}</p>
                  </div>
                )}

                {selectedIncident.impact_description && (
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-2">وصف التأثير</label>
                    <p className="text-white bg-dark-700 p-3 rounded-lg">{selectedIncident.impact_description}</p>
                  </div>
                )}

                {selectedIncident.actions_taken && (
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-2">الإجراءات المتخذة</label>
                    <p className="text-white bg-dark-700 p-3 rounded-lg">{selectedIncident.actions_taken}</p>
                  </div>
                )}

                {selectedIncident.lessons_learned && (
                  <div>
                    <label className="block text-sm font-medium text-dark-300 mb-2">الدروس المستفادة</label>
                    <p className="text-white bg-dark-700 p-3 rounded-lg">{selectedIncident.lessons_learned}</p>
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end space-x-4 space-x-reverse">
                <button
                  onClick={() => setSelectedIncident(null)}
                  className="btn-secondary"
                >
                  إغلاق
                </button>
                {(user?.role === 'admin' || selectedIncident.created_by === user?.id) && (
                  <Link
                    to={`/incidents/edit/${selectedIncident.id}`}
                    className="btn-primary"
                  >
                    تعديل الحادث
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default IncidentList
