from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from typing import List, Optional
from datetime import datetime

from app.models.user import User, UserRole
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password


def get_user(db: Session, user_id: int) -> Optional[User]:
    """الحصول على مستخدم بالمعرف"""
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """الحصول على مستخدم باسم المستخدم"""
    return db.query(User).filter(User.username == username).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """الحصول على مستخدم بالبريد الإلكتروني"""
    return db.query(User).filter(User.email == email).first()


def get_users(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    role: Optional[UserRole] = None,
    is_active: Optional[bool] = None
) -> List[User]:
    """الحصول على قائمة المستخدمين"""
    query = db.query(User)
    
    # البحث النصي
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                User.full_name.ilike(search_term)
            )
        )
    
    # فلترة حسب الدور
    if role:
        query = query.filter(User.role == role)
    
    # فلترة حسب الحالة
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    return query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()


def create_user(db: Session, user: UserCreate) -> User:
    """إنشاء مستخدم جديد"""
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        role=user.role,
        is_active=user.is_active
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """تحديث مستخدم"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.dict(exclude_unset=True)
    
    # تشفير كلمة المرور الجديدة إذا تم تقديمها
    if 'password' in update_data:
        update_data['hashed_password'] = get_password_hash(update_data.pop('password'))
    
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    return db_user


def delete_user(db: Session, user_id: int) -> bool:
    """حذف مستخدم"""
    db_user = get_user(db, user_id)
    if not db_user:
        return False
    
    db.delete(db_user)
    db.commit()
    return True


def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """التحقق من صحة بيانات المستخدم"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def get_users_count(db: Session) -> int:
    """الحصول على عدد المستخدمين"""
    return db.query(User).count()


def get_active_users_count(db: Session) -> int:
    """الحصول على عدد المستخدمين النشطين"""
    return db.query(User).filter(User.is_active == True).count()


def get_users_by_role(db: Session, role: UserRole) -> List[User]:
    """الحصول على المستخدمين حسب الدور"""
    return db.query(User).filter(User.role == role).all()


def activate_user(db: Session, user_id: int) -> Optional[User]:
    """تفعيل مستخدم"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    db_user.is_active = True
    db_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    return db_user


def deactivate_user(db: Session, user_id: int) -> Optional[User]:
    """إلغاء تفعيل مستخدم"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    db_user.is_active = False
    db_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    return db_user


def change_user_password(db: Session, user_id: int, new_password: str) -> Optional[User]:
    """تغيير كلمة مرور المستخدم"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    db_user.hashed_password = get_password_hash(new_password)
    db_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user_statistics(db: Session) -> dict:
    """الحصول على إحصائيات المستخدمين"""
    total_users = get_users_count(db)
    active_users = get_active_users_count(db)
    
    # إحصائيات حسب الدور
    role_stats = {}
    for role in UserRole:
        count = db.query(User).filter(User.role == role).count()
        role_stats[role.value] = count
    
    # المستخدمين الجدد هذا الشهر
    start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_users_this_month = db.query(User).filter(User.created_at >= start_of_month).count()
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "inactive_users": total_users - active_users,
        "role_statistics": role_stats,
        "new_users_this_month": new_users_this_month
    }
