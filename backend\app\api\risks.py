from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models.user import User
from app.api.auth import get_current_active_user
from app.schemas.risk import (
    RiskCreate, RiskUpdate, RiskResponse, RiskListResponse, RiskSummary,
    RiskSearchParams, RiskFilter, RiskMatrix, RiskAssessmentCreate, RiskAssessmentResponse,
    RiskControlCreate, RiskControlResponse, RiskIncidentCreate, RiskIncidentResponse
)
from app.crud import risk as risk_crud

router = APIRouter()


@router.get("/", response_model=dict)
async def get_risks(
    search: Optional[str] = Query(None, description="البحث في العنوان والوصف"),
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(10, ge=1, le=100, description="عدد العناصر في الصفحة"),
    sort_by: Optional[str] = Query("created_at", description="ترتيب حسب"),
    sort_order: Optional[str] = Query("desc", regex="^(asc|desc)$", description="اتجاه الترتيب"),
    risk_type: Optional[str] = Query(None, description="نوع المخاطر"),
    risk_level: Optional[str] = Query(None, description="مستوى المخاطر"),
    status: Optional[str] = Query(None, description="حالة المخاطر"),
    owner_id: Optional[int] = Query(None, description="معرف المالك"),
    business_unit: Optional[str] = Query(None, description="الوحدة التجارية"),
    overdue_only: Optional[bool] = Query(False, description="المتأخرة فقط"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على قائمة المخاطر مع الفلترة والبحث"""
    
    # إنشاء معاملات البحث
    filters = RiskFilter(
        risk_type=risk_type,
        risk_level=risk_level,
        status=status,
        owner_id=owner_id,
        business_unit=business_unit,
        overdue_only=overdue_only
    )
    
    search_params = RiskSearchParams(
        search=search,
        page=page,
        per_page=per_page,
        sort_by=sort_by,
        sort_order=sort_order,
        filters=filters
    )
    
    risks, total = risk_crud.get_risks(db, search_params, current_user)
    
    return {
        "items": risks,
        "total": total,
        "page": page,
        "per_page": per_page,
        "pages": (total + per_page - 1) // per_page
    }


@router.get("/summary", response_model=RiskSummary)
async def get_risks_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على ملخص المخاطر"""
    return risk_crud.get_risk_summary(db, current_user)


@router.get("/matrix", response_model=RiskMatrix)
async def get_risk_matrix(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على مصفوفة المخاطر"""
    matrix = risk_crud.get_risk_matrix(db, current_user)
    summary = risk_crud.get_risk_summary(db, current_user)
    
    return {
        "matrix": matrix,
        "summary": summary
    }


@router.post("/", response_model=RiskResponse)
async def create_risk(
    risk: RiskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء مخاطرة جديدة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء المخاطر"
        )
    
    # التحقق من وجود المالك
    from app.crud.users import get_user
    owner = get_user(db, risk.owner_id)
    if not owner:
        raise HTTPException(
            status_code=404,
            detail="المالك المحدد غير موجود"
        )
    
    return risk_crud.create_risk(db, risk, current_user)


@router.get("/{risk_id}", response_model=RiskResponse)
async def get_risk(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على مخاطرة بالمعرف"""
    
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(
            status_code=404,
            detail="المخاطرة غير موجودة"
        )
    
    # التحقق من الصلاحيات
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        db_risk.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض هذه المخاطرة"
        )
    
    return db_risk


@router.put("/{risk_id}", response_model=RiskResponse)
async def update_risk(
    risk_id: int,
    risk_update: RiskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تحديث مخاطرة"""
    
    db_risk = risk_crud.update_risk(db, risk_id, risk_update, current_user)
    if not db_risk:
        raise HTTPException(
            status_code=404,
            detail="المخاطرة غير موجودة أو ليس لديك صلاحية لتحديثها"
        )
    
    return db_risk


@router.delete("/{risk_id}")
async def delete_risk(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """حذف مخاطرة"""
    
    success = risk_crud.delete_risk(db, risk_id, current_user)
    if not success:
        raise HTTPException(
            status_code=404,
            detail="المخاطرة غير موجودة أو ليس لديك صلاحية لحذفها"
        )
    
    return {"message": "تم حذف المخاطرة بنجاح"}


# Risk Assessments
@router.get("/{risk_id}/assessments", response_model=List[RiskAssessmentResponse])
async def get_risk_assessments(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على تقييمات المخاطرة"""
    
    # التحقق من وجود المخاطرة والصلاحيات
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        db_risk.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض تقييمات هذه المخاطرة"
        )
    
    return risk_crud.get_risk_assessments(db, risk_id)


@router.post("/{risk_id}/assessments", response_model=RiskAssessmentResponse)
async def create_risk_assessment(
    risk_id: int,
    assessment: RiskAssessmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء تقييم جديد للمخاطرة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء تقييمات المخاطر"
        )
    
    # التحقق من وجود المخاطرة
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    # تعديل معرف المخاطرة في التقييم
    assessment.risk_id = risk_id
    
    from app.crud.risk import create_assessment_from_data
    return create_assessment_from_data(db, assessment, current_user)


# Risk Controls
@router.get("/{risk_id}/controls", response_model=List[RiskControlResponse])
async def get_risk_controls(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على ضوابط المخاطرة"""
    
    # التحقق من وجود المخاطرة والصلاحيات
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        db_risk.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض ضوابط هذه المخاطرة"
        )
    
    return db_risk.controls


@router.post("/{risk_id}/controls", response_model=RiskControlResponse)
async def create_risk_control(
    risk_id: int,
    control: RiskControlCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء ضابطة جديدة للمخاطرة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لإنشاء ضوابط المخاطر"
        )
    
    # التحقق من وجود المخاطرة
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    # تعديل معرف المخاطرة في الضابطة
    control.risk_id = risk_id
    
    from app.crud.risk import create_risk_control
    return create_risk_control(db, control, current_user)


# Risk Incidents
@router.get("/{risk_id}/incidents", response_model=List[RiskIncidentResponse])
async def get_risk_incidents(
    risk_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على حوادث المخاطرة"""
    
    # التحقق من وجود المخاطرة والصلاحيات
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    if (current_user.role not in ["ADMIN", "RISK_MANAGER"] and 
        db_risk.owner_id != current_user.id):
        raise HTTPException(
            status_code=403,
            detail="ليس لديك صلاحية لعرض حوادث هذه المخاطرة"
        )
    
    return db_risk.incidents


@router.post("/{risk_id}/incidents", response_model=RiskIncidentResponse)
async def create_risk_incident(
    risk_id: int,
    incident: RiskIncidentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء حادث جديد للمخاطرة"""
    
    # التحقق من وجود المخاطرة
    db_risk = risk_crud.get_risk(db, risk_id)
    if not db_risk:
        raise HTTPException(status_code=404, detail="المخاطرة غير موجودة")
    
    # تعديل معرف المخاطرة في الحادث
    incident.risk_id = risk_id
    
    from app.crud.risk import create_risk_incident
    return create_risk_incident(db, incident, current_user)


# Additional helper functions for risk CRUD operations
def create_assessment_from_data(db: Session, assessment: RiskAssessmentCreate, current_user: User):
    """إنشاء تقييم من البيانات"""
    from app.models.risk import RiskAssessment, RiskLevel

    db_assessment = RiskAssessment(
        risk_id=assessment.risk_id,
        probability=assessment.probability,
        impact=assessment.impact,
        risk_score=assessment.probability * assessment.impact,
        assessment_notes=assessment.assessment_notes,
        methodology=assessment.methodology,
        assessor_id=current_user.id
    )

    # تحديد مستوى المخاطر
    if db_assessment.risk_score <= 4:
        db_assessment.risk_level = RiskLevel.VERY_LOW
    elif db_assessment.risk_score <= 8:
        db_assessment.risk_level = RiskLevel.LOW
    elif db_assessment.risk_score <= 12:
        db_assessment.risk_level = RiskLevel.MEDIUM
    elif db_assessment.risk_score <= 16:
        db_assessment.risk_level = RiskLevel.HIGH
    elif db_assessment.risk_score <= 20:
        db_assessment.risk_level = RiskLevel.VERY_HIGH
    else:
        db_assessment.risk_level = RiskLevel.CRITICAL

    db.add(db_assessment)
    db.commit()
    db.refresh(db_assessment)
    return db_assessment


def create_risk_control(db: Session, control: RiskControlCreate, current_user: User):
    """إنشاء ضابطة مخاطر"""
    from app.models.risk import RiskControl

    db_control = RiskControl(
        risk_id=control.risk_id,
        title=control.title,
        description=control.description,
        control_type=control.control_type,
        effectiveness=control.effectiveness,
        implementation_status=control.implementation_status,
        owner_id=control.owner_id
    )

    db.add(db_control)
    db.commit()
    db.refresh(db_control)
    return db_control


def create_risk_incident(db: Session, incident: RiskIncidentCreate, current_user: User):
    """إنشاء حادث مخاطر"""
    from app.models.risk import RiskIncident

    db_incident = RiskIncident(
        risk_id=incident.risk_id,
        title=incident.title,
        description=incident.description,
        incident_date=incident.incident_date,
        severity=incident.severity,
        impact_description=incident.impact_description,
        response_actions=incident.response_actions,
        lessons_learned=incident.lessons_learned,
        reported_by_id=current_user.id,
        assigned_to_id=incident.assigned_to_id
    )

    db.add(db_incident)
    db.commit()
    db.refresh(db_incident)
    return db_incident
