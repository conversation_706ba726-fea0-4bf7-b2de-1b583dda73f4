"""
إعداد قاعدة البيانات SQLite
"""
import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
database_dir = os.path.dirname(settings.database_url.replace("sqlite:///", ""))
if database_dir and not os.path.exists(database_dir):
    os.makedirs(database_dir)

# إنشاء محرك قاعدة البيانات
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False},  # مطلوب لـ SQLite
    echo=settings.debug  # طباعة استعلامات SQL في وضع التطوير
)

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# إنشاء الفئة الأساسية للنماذج
Base = declarative_base()


def get_db():
    """
    الحصول على جلسة قاعدة البيانات
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """
    إنشاء جداول قاعدة البيانات
    """
    Base.metadata.create_all(bind=engine)
