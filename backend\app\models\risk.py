from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, Foreign<PERSON><PERSON>, Float, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum


class RiskType(str, enum.Enum):
    """أنواع المخاطر"""
    OPERATIONAL = "operational"  # تشغيلية
    FINANCIAL = "financial"  # مالية
    STRATEGIC = "strategic"  # استراتيجية
    COMPLIANCE = "compliance"  # امتثال
    TECHNOLOGY = "technology"  # تقنية
    REPUTATION = "reputation"  # سمعة
    SECURITY = "security"  # أمنية
    ENVIRONMENTAL = "environmental"  # بيئية
    LEGAL = "legal"  # قانونية
    HUMAN_RESOURCES = "human_resources"  # موارد بشرية


class RiskStatus(str, enum.Enum):
    """حالات المخاطر"""
    IDENTIFIED = "identified"  # محددة
    ASSESSED = "assessed"  # مقيمة
    MITIGATED = "mitigated"  # مخففة
    MONITORED = "monitored"  # مراقبة
    CLOSED = "closed"  # مغلقة
    ESCALATED = "escalated"  # مصعدة


class RiskLevel(str, enum.Enum):
    """مستويات المخاطر"""
    VERY_LOW = "very_low"  # منخفضة جداً
    LOW = "low"  # منخفضة
    MEDIUM = "medium"  # متوسطة
    HIGH = "high"  # عالية
    VERY_HIGH = "very_high"  # عالية جداً
    CRITICAL = "critical"  # حرجة


class Risk(Base):
    """نموذج المخاطر الرئيسي"""
    __tablename__ = "risks"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=False)
    risk_type = Column(Enum(RiskType), nullable=False, index=True)
    
    # تقييم المخاطر
    probability = Column(Integer, nullable=False)  # الاحتمالية (1-5)
    impact = Column(Integer, nullable=False)  # التأثير (1-5)
    risk_score = Column(Float, nullable=False)  # النتيجة المحسوبة
    risk_level = Column(Enum(RiskLevel), nullable=False, index=True)
    
    # المسؤوليات والتواريخ
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    identified_date = Column(DateTime(timezone=True), server_default=func.now())
    last_review_date = Column(DateTime(timezone=True))
    next_review_date = Column(DateTime(timezone=True))
    
    # الحالة والمتابعة
    status = Column(Enum(RiskStatus), default=RiskStatus.IDENTIFIED, index=True)
    is_active = Column(Boolean, default=True, index=True)
    
    # معلومات إضافية
    business_unit = Column(String(100))  # الوحدة التجارية
    category = Column(String(100))  # الفئة
    source = Column(String(100))  # المصدر
    
    # التواريخ الأساسية
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by_id = Column(Integer, ForeignKey("users.id"))

    # العلاقات
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_risks")
    created_by = relationship("User", foreign_keys=[created_by_id])
    updated_by = relationship("User", foreign_keys=[updated_by_id])
    
    # علاقات مع الجداول الأخرى
    assessments = relationship("RiskAssessment", back_populates="risk", cascade="all, delete-orphan")
    mitigation_plans = relationship("MitigationPlan", back_populates="risk", cascade="all, delete-orphan")
    controls = relationship("RiskControl", back_populates="risk", cascade="all, delete-orphan")
    incidents = relationship("RiskIncident", back_populates="risk", cascade="all, delete-orphan")

    def calculate_risk_score(self):
        """حساب نتيجة المخاطر"""
        self.risk_score = self.probability * self.impact
        
        # تحديد مستوى المخاطر بناءً على النتيجة
        if self.risk_score <= 4:
            self.risk_level = RiskLevel.VERY_LOW
        elif self.risk_score <= 8:
            self.risk_level = RiskLevel.LOW
        elif self.risk_score <= 12:
            self.risk_level = RiskLevel.MEDIUM
        elif self.risk_score <= 16:
            self.risk_level = RiskLevel.HIGH
        elif self.risk_score <= 20:
            self.risk_level = RiskLevel.VERY_HIGH
        else:
            self.risk_level = RiskLevel.CRITICAL

    def __repr__(self):
        return f"<Risk(id={self.id}, title='{self.title}', level='{self.risk_level}')>"


class RiskAssessment(Base):
    """تقييمات المخاطر التاريخية"""
    __tablename__ = "risk_assessments"

    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    # تقييم المخاطر
    probability = Column(Integer, nullable=False)
    impact = Column(Integer, nullable=False)
    risk_score = Column(Float, nullable=False)
    risk_level = Column(Enum(RiskLevel), nullable=False)
    
    # التفاصيل
    assessment_notes = Column(Text)
    methodology = Column(String(100))  # منهجية التقييم
    
    # التواريخ والمسؤوليات
    assessment_date = Column(DateTime(timezone=True), server_default=func.now())
    assessor_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    approved_by_id = Column(Integer, ForeignKey("users.id"))
    approval_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    risk = relationship("Risk", back_populates="assessments")
    assessor = relationship("User", foreign_keys=[assessor_id])
    approved_by = relationship("User", foreign_keys=[approved_by_id])

    def __repr__(self):
        return f"<RiskAssessment(id={self.id}, risk_id={self.risk_id}, score={self.risk_score})>"


class RiskControl(Base):
    """ضوابط المخاطر"""
    __tablename__ = "risk_controls"

    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    control_type = Column(String(50), nullable=False)  # preventive, detective, corrective
    
    # فعالية الضابطة
    effectiveness = Column(Integer)  # 1-5
    implementation_status = Column(String(50))  # planned, implemented, tested, effective
    
    # المسؤوليات
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # التواريخ
    implementation_date = Column(DateTime(timezone=True))
    last_test_date = Column(DateTime(timezone=True))
    next_test_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    risk = relationship("Risk", back_populates="controls")
    owner = relationship("User")

    def __repr__(self):
        return f"<RiskControl(id={self.id}, title='{self.title}')>"


class RiskIncident(Base):
    """حوادث المخاطر"""
    __tablename__ = "risk_incidents"

    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    
    # تفاصيل الحادث
    incident_date = Column(DateTime(timezone=True), nullable=False)
    severity = Column(String(50), nullable=False)  # low, medium, high, critical
    impact_description = Column(Text)
    
    # الاستجابة
    response_actions = Column(Text)
    lessons_learned = Column(Text)
    
    # الحالة
    status = Column(String(50), default="open")  # open, investigating, resolved, closed
    
    # المسؤوليات
    reported_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    assigned_to_id = Column(Integer, ForeignKey("users.id"))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    risk = relationship("Risk", back_populates="incidents")
    reported_by = relationship("User", foreign_keys=[reported_by_id])
    assigned_to = relationship("User", foreign_keys=[assigned_to_id])

    def __repr__(self):
        return f"<RiskIncident(id={self.id}, title='{self.title}')>"
