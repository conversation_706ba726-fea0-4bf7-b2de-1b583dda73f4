# نظام إدارة الحوكمة والمخاطر والامتثال (GRC)

نظام ويب متكامل لإدارة الحوكمة والمخاطر والامتثال، مخصص للامتثال للمعايير السعودية مثل NCA ECC و ISO 27001، مع وحدة متخصصة لتسجيل الحوادث السيبرانية.

## 🌟 المميزات

- **واجهة عربية احترافية**: تصميم داكن فخم مع دعم كامل للغة العربية
- **إدارة الحوادث السيبرانية**: تسجيل ومتابعة الحوادث الأمنية
- **نظام أدوار متقدم**: مدير ومستخدم عادي مع صلاحيات مختلفة
- **أمان عالي**: تشفير كلمات المرور وحماية ضد XSS و SQL Injection
- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات
- **امتثال للمعايير**: متوافق مع NCA ECC و ISO 27001

## 🏗️ التقنيات المستخدمة

### Backend
- **FastAPI**: إطار عمل Python سريع وحديث
- **SQLAlchemy**: ORM للتعامل مع قاعدة البيانات
- **SQLite**: قاعدة بيانات خفيفة وسهلة الإعداد
- **JWT**: للمصادقة والتوثيق
- **Bcrypt**: لتشفير كلمات المرور

### Frontend
- **React 18**: مكتبة JavaScript للواجهات
- **TailwindCSS**: إطار عمل CSS للتصميم
- **Vite**: أداة بناء سريعة
- **React Router**: للتنقل بين الصفحات
- **Axios**: للتواصل مع API

## 📋 المتطلبات

- Python 3.8 أو أحدث
- Node.js 16 أو أحدث
- npm أو yarn

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع

```bash
git clone <repository-url>
cd grc-system
```

### 2. إعداد Backend

```bash
# الانتقال إلى مجلد Backend
cd backend

# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على macOS/Linux:
source venv/bin/activate

# تثبيت المكتبات
pip install -r requirements.txt

# تشغيل الخادم
python run.py
```

الخادم سيعمل على: `http://localhost:8000`

### 3. إعداد Frontend

```bash
# فتح terminal جديد والانتقال إلى مجلد Frontend
cd frontend

# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm run dev
```

التطبيق سيعمل على: `http://localhost:3000`

## 👤 بيانات المدير الافتراضي

```
اسم المستخدم: admin
كلمة المرور: Admin@123
```

## 📚 استخدام النظام

### تسجيل الدخول
1. افتح المتصفح وانتقل إلى `http://localhost:3000`
2. استخدم بيانات المدير الافتراضي للدخول
3. ستنتقل إلى لوحة التحكم الرئيسية

### إدارة الحوادث
1. **تسجيل حادث جديد**: اضغط على "تسجيل حادث جديد"
2. **عرض الحوادث**: انتقل إلى "الحوادث السيبرانية"
3. **تعديل حادث**: اضغط على أيقونة التعديل
4. **عرض التفاصيل**: اضغط على أيقونة العين

### إدارة المستخدمين (للمديرين فقط)
1. انتقل إلى قسم المستخدمين
2. يمكن إضافة مستخدمين جدد
3. تعديل صلاحيات المستخدمين
4. تفعيل/إلغاء تفعيل الحسابات

## 🔧 الإعدادات

### متغيرات البيئة (Backend)

قم بتعديل ملف `backend/.env`:

```env
# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///./database/grc_system.db

# إعدادات الحماية
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# إعدادات التطبيق
APP_NAME=نظام إدارة الحوكمة والمخاطر والامتثال
DEBUG=True

# إعدادات CORS
ALLOWED_ORIGINS=["http://localhost:3000"]
```

## 📊 هيكل قاعدة البيانات

### جدول المستخدمين (users)
- id: المعرف الفريد
- username: اسم المستخدم
- email: البريد الإلكتروني
- full_name: الاسم الكامل
- hashed_password: كلمة المرور المشفرة
- role: الدور (admin/user)
- is_active: حالة التفعيل

### جدول الحوادث (incidents)
- id: المعرف الفريد
- title: عنوان الحادث
- description: وصف الحادث
- incident_type: نوع الحادث
- severity: مستوى الخطورة
- status: حالة الحادث
- incident_date: تاريخ الحادث
- created_by: المُبلِغ
- reference_number: الرقم المرجعي

### جدول السجلات (logs)
- id: المعرف الفريد
- level: مستوى السجل
- action: نوع العملية
- message: الرسالة
- user_id: المستخدم
- created_at: تاريخ الإنشاء

## 🔒 الأمان

- **تشفير كلمات المرور**: باستخدام Bcrypt
- **JWT Authentication**: للمصادقة الآمنة
- **حماية CORS**: تحديد المصادر المسموحة
- **تنظيف المدخلات**: منع XSS attacks
- **استعلامات آمنة**: منع SQL Injection

## 🚀 النشر في الإنتاج

### Backend
```bash
# تثبيت gunicorn
pip install gunicorn

# تشغيل الخادم
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Frontend
```bash
# بناء التطبيق
npm run build

# نسخ ملفات dist إلى خادم الويب
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**ملاحظة**: هذا النظام مصمم للامتثال للمعايير السعودية ويمكن تخصيصه حسب احتياجات المؤسسة.
