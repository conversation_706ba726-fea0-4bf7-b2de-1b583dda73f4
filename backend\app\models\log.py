"""
نموذج سجلات النظام
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class LogLevel(str, enum.Enum):
    """مستوى السجل"""
    INFO = "info"        # معلومات
    WARNING = "warning"  # تحذير
    ERROR = "error"      # خطأ
    CRITICAL = "critical"  # حرج


class LogAction(str, enum.Enum):
    """نوع العملية"""
    LOGIN = "login"                    # تسجيل دخول
    LOGOUT = "logout"                  # تسجيل خروج
    CREATE_INCIDENT = "create_incident"  # إنشاء حادث
    UPDATE_INCIDENT = "update_incident"  # تحديث حادث
    DELETE_INCIDENT = "delete_incident"  # حذف حادث
    VIEW_INCIDENT = "view_incident"      # عرض حادث
    CREATE_USER = "create_user"          # إنشاء مستخدم
    UPDATE_USER = "update_user"          # تحديث مستخدم
    DELETE_USER = "delete_user"          # حذف مستخدم
    EXPORT_DATA = "export_data"          # تصدير بيانات
    SYSTEM_ERROR = "system_error"        # خطأ في النظام


class Log(Base):
    """نموذج سجل النظام"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(Enum(LogLevel), default=LogLevel.INFO, nullable=False)
    action = Column(Enum(LogAction), nullable=False)
    message = Column(Text, nullable=False)
    details = Column(Text, nullable=True)  # تفاصيل إضافية
    
    # معلومات المستخدم
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    user_ip = Column(String(45), nullable=True)  # عنوان IP
    user_agent = Column(String(500), nullable=True)  # معلومات المتصفح
    
    # معلومات الجلسة
    session_id = Column(String(100), nullable=True)
    
    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    user = relationship("User", back_populates="logs")
    
    def __repr__(self):
        return f"<Log(action='{self.action}', level='{self.level}', user_id={self.user_id})>"
