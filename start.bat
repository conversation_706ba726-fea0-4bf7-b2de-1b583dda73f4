@echo off
echo ========================================
echo   نظام إدارة الحوكمة والمخاطر والامتثال
echo ========================================
echo.

echo تشغيل Backend...
cd backend
start cmd /k "python run.py"
cd ..

echo انتظار 5 ثوان لتشغيل Backend...
timeout /t 5 /nobreak > nul

echo تشغيل Frontend...
cd frontend
start cmd /k "npm run dev"
cd ..

echo.
echo تم تشغيل النظام بنجاح!
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo بيانات المدير الافتراضي:
echo اسم المستخدم: admin
echo كلمة المرور: Admin@123
echo.
pause
