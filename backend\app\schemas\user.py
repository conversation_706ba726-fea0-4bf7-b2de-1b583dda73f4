"""
مخططات المستخدمين
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator
from ..models.user import UserRole


class UserBase(BaseModel):
    """المخطط الأساسي للمستخدم"""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    is_active: bool = True


class UserCreate(UserBase):
    """مخطط إنشاء مستخدم"""
    password: str
    
    @validator('username')
    def username_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('اسم المستخدم مطلوب')
        if len(v.strip()) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        return v.strip()
    
    @validator('password')
    def password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        return v


class UserUpdate(BaseModel):
    """مخطط تحديث المستخدم"""
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class UserInDB(UserBase):
    """مخطط المستخدم في قاعدة البيانات"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class User(UserInDB):
    """مخطط المستخدم للعرض"""
    pass


class UserWithStats(User):
    """مخطط المستخدم مع الإحصائيات"""
    incidents_count: int = 0
    last_login: Optional[datetime] = None
