from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum


class MitigationStrategy(str, Enum):
    """استراتيجيات التخفيف"""
    AVOID = "avoid"
    MITIGATE = "mitigate"
    TRANSFER = "transfer"
    ACCEPT = "accept"
    MONITOR = "monitor"


class ActionStatus(str, Enum):
    """حالات الإجراءات"""
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class Priority(str, Enum):
    """أولويات الإجراءات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Mitigation Plan schemas
class MitigationPlanBase(BaseModel):
    """المخطط الأساسي لخطط التخفيف"""
    title: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    strategy: MitigationStrategy
    objective: Optional[str] = None
    success_criteria: Optional[str] = None
    target_risk_reduction: Optional[float] = Field(None, ge=0, le=100)
    estimated_cost: Optional[float] = Field(None, ge=0)
    resources_required: Optional[str] = None
    start_date: Optional[datetime] = None
    target_completion_date: Optional[datetime] = None


class MitigationPlanCreate(MitigationPlanBase):
    """مخطط إنشاء خطة التخفيف"""
    risk_id: int = Field(..., gt=0)
    owner_id: int = Field(..., gt=0)
    sponsor_id: Optional[int] = Field(None, gt=0)


class MitigationPlanUpdate(BaseModel):
    """مخطط تحديث خطة التخفيف"""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1)
    strategy: Optional[MitigationStrategy] = None
    objective: Optional[str] = None
    success_criteria: Optional[str] = None
    target_risk_reduction: Optional[float] = Field(None, ge=0, le=100)
    estimated_cost: Optional[float] = Field(None, ge=0)
    resources_required: Optional[str] = None
    start_date: Optional[datetime] = None
    target_completion_date: Optional[datetime] = None
    status: Optional[str] = None
    budget_approved: Optional[bool] = None
    owner_id: Optional[int] = Field(None, gt=0)
    sponsor_id: Optional[int] = Field(None, gt=0)


class UserBasic(BaseModel):
    """معلومات المستخدم الأساسية"""
    id: int
    username: str
    full_name: Optional[str]
    email: str

    class Config:
        from_attributes = True


class MitigationPlanResponse(MitigationPlanBase):
    """استجابة خطة التخفيف"""
    id: int
    risk_id: int
    budget_approved: bool
    status: str
    progress_percentage: float
    actual_completion_date: Optional[datetime]
    approved_by: Optional[UserBasic]
    approval_date: Optional[datetime]
    last_review_date: Optional[datetime]
    next_review_date: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    
    # معلومات المستخدمين
    owner: UserBasic
    sponsor: Optional[UserBasic]
    created_by: UserBasic

    class Config:
        from_attributes = True


class MitigationPlanListResponse(BaseModel):
    """استجابة قائمة خطط التخفيف"""
    id: int
    title: str
    strategy: MitigationStrategy
    status: str
    progress_percentage: float
    owner: UserBasic
    target_completion_date: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


# Mitigation Action schemas
class MitigationActionBase(BaseModel):
    """المخطط الأساسي لإجراءات التخفيف"""
    title: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    action_type: Optional[str] = Field(None, max_length=50)
    priority: Priority = Priority.MEDIUM
    start_date: Optional[datetime] = None
    due_date: datetime
    estimated_effort_hours: Optional[float] = Field(None, ge=0)
    cost: Optional[float] = Field(None, ge=0)


class MitigationActionCreate(MitigationActionBase):
    """مخطط إنشاء إجراء التخفيف"""
    plan_id: int = Field(..., gt=0)
    assigned_to_id: int = Field(..., gt=0)
    reviewer_id: Optional[int] = Field(None, gt=0)


class MitigationActionUpdate(BaseModel):
    """مخطط تحديث إجراء التخفيف"""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1)
    action_type: Optional[str] = Field(None, max_length=50)
    priority: Optional[Priority] = None
    start_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    status: Optional[ActionStatus] = None
    progress_notes: Optional[str] = None
    completion_evidence: Optional[str] = None
    estimated_effort_hours: Optional[float] = Field(None, ge=0)
    actual_effort_hours: Optional[float] = Field(None, ge=0)
    cost: Optional[float] = Field(None, ge=0)
    assigned_to_id: Optional[int] = Field(None, gt=0)
    reviewer_id: Optional[int] = Field(None, gt=0)


class MitigationActionResponse(MitigationActionBase):
    """استجابة إجراء التخفيف"""
    id: int
    plan_id: int
    status: ActionStatus
    progress_notes: Optional[str]
    completion_evidence: Optional[str]
    completion_date: Optional[datetime]
    actual_effort_hours: Optional[float]
    created_at: datetime
    updated_at: Optional[datetime]
    
    # معلومات المستخدمين
    assigned_to: UserBasic
    reviewer: Optional[UserBasic]

    class Config:
        from_attributes = True


class MitigationActionListResponse(BaseModel):
    """استجابة قائمة إجراءات التخفيف"""
    id: int
    title: str
    priority: Priority
    status: ActionStatus
    assigned_to: UserBasic
    due_date: datetime
    completion_date: Optional[datetime]
    is_overdue: bool

    class Config:
        from_attributes = True


# Action Update schemas
class ActionUpdateBase(BaseModel):
    """المخطط الأساسي لتحديثات الإجراءات"""
    update_text: str = Field(..., min_length=1)
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    attachments: Optional[str] = None


class ActionUpdateCreate(ActionUpdateBase):
    """مخطط إنشاء تحديث الإجراء"""
    action_id: int = Field(..., gt=0)


class ActionUpdateResponse(ActionUpdateBase):
    """استجابة تحديث الإجراء"""
    id: int
    action_id: int
    updated_by: UserBasic
    created_at: datetime

    class Config:
        from_attributes = True


# Plan Review schemas
class PlanReviewBase(BaseModel):
    """المخطط الأساسي لمراجعات الخطط"""
    review_date: datetime
    effectiveness_rating: Optional[int] = Field(None, ge=1, le=5)
    progress_assessment: Optional[str] = None
    issues_identified: Optional[str] = None
    recommendations: Optional[str] = None
    continue_plan: bool = True
    modify_plan: bool = False
    escalate: bool = False
    next_review_date: Optional[datetime] = None


class PlanReviewCreate(PlanReviewBase):
    """مخطط إنشاء مراجعة الخطة"""
    plan_id: int = Field(..., gt=0)


class PlanReviewResponse(PlanReviewBase):
    """استجابة مراجعة الخطة"""
    id: int
    plan_id: int
    reviewer: UserBasic
    created_at: datetime

    class Config:
        from_attributes = True


# Risk Treatment schemas
class RiskTreatmentBase(BaseModel):
    """المخطط الأساسي لمعالجة المخاطر"""
    treatment_strategy: MitigationStrategy
    justification: Optional[str] = None
    residual_probability: Optional[int] = Field(None, ge=1, le=5)
    residual_impact: Optional[int] = Field(None, ge=1, le=5)
    implementation_date: Optional[datetime] = None
    review_date: Optional[datetime] = None


class RiskTreatmentCreate(RiskTreatmentBase):
    """مخطط إنشاء معالجة المخاطر"""
    risk_id: int = Field(..., gt=0)


class RiskTreatmentResponse(RiskTreatmentBase):
    """استجابة معالجة المخاطر"""
    id: int
    risk_id: int
    residual_risk_score: Optional[float]
    approved_by: Optional[UserBasic]
    approval_date: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


# Summary and Dashboard schemas
class MitigationSummary(BaseModel):
    """ملخص خطط التخفيف"""
    total_plans: int
    by_strategy: dict
    by_status: dict
    average_progress: float
    overdue_actions: int
    completed_plans: int


class ActionsSummary(BaseModel):
    """ملخص الإجراءات"""
    total_actions: int
    by_status: dict
    by_priority: dict
    overdue_count: int
    completed_this_month: int


# Filter schemas
class MitigationFilter(BaseModel):
    """مرشحات خطط التخفيف"""
    strategy: Optional[MitigationStrategy] = None
    status: Optional[str] = None
    owner_id: Optional[int] = None
    risk_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    overdue_only: Optional[bool] = False


class ActionFilter(BaseModel):
    """مرشحات الإجراءات"""
    status: Optional[ActionStatus] = None
    priority: Optional[Priority] = None
    assigned_to_id: Optional[int] = None
    plan_id: Optional[int] = None
    due_date_from: Optional[datetime] = None
    due_date_to: Optional[datetime] = None
    overdue_only: Optional[bool] = False
