import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  HomeIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  UserIcon,
  UsersIcon,
  ShieldExclamationIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline'
import { ROUTES } from '../../utils/constants'
import { PERMISSIONS, hasPermission } from '../../utils/permissions'
import Breadcrumb from './Breadcrumb'
import PermissionGuard from '../auth/PermissionGuard'

const Layout = ({ children, user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState({})
  const location = useLocation()

  const toggleMenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }))
  }

  const navigation = [
    {
      name: 'لوحة التحكم',
      href: ROUTES.DASHBOARD,
      icon: HomeIcon,
      type: 'single',
      description: 'نظرة عامة على النظام',
      permission: PERMISSIONS.DASHBOARD_VIEW
    },
    {
      name: 'إدارة الحوادث',
      icon: ExclamationTriangleIcon,
      type: 'group',
      key: 'incidents',
      description: 'إدارة الحوادث السيبرانية',
      permission: PERMISSIONS.INCIDENTS_VIEW,
      children: [
        {
          name: 'جميع الحوادث',
          href: ROUTES.INCIDENTS,
          description: 'عرض وإدارة جميع الحوادث',
          permission: PERMISSIONS.INCIDENTS_VIEW
        },
        {
          name: 'إضافة حادث جديد',
          href: ROUTES.INCIDENT_CREATE,
          description: 'تسجيل حادث سيبراني جديد',
          permission: PERMISSIONS.INCIDENTS_CREATE
        }
      ]
    },
    {
      name: 'إدارة المخاطر',
      icon: ShieldExclamationIcon,
      type: 'group',
      key: 'risks',
      description: 'تحديد وتقييم وإدارة المخاطر',
      children: [
        {
          name: 'جميع المخاطر',
          href: ROUTES.RISKS,
          description: 'عرض وإدارة جميع المخاطر'
        },
        {
          name: 'إضافة مخاطرة',
          href: ROUTES.RISK_CREATE,
          description: 'تسجيل مخاطرة جديدة'
        },
        {
          name: 'مصفوفة المخاطر',
          href: ROUTES.RISK_MATRIX,
          description: 'عرض مصفوفة تقييم المخاطر'
        },
        {
          name: 'تقييم المخاطر',
          href: ROUTES.RISK_ASSESSMENT,
          description: 'أدوات تقييم المخاطر'
        }
      ]
    },
    {
      name: 'الحوكمة والامتثال',
      icon: DocumentTextIcon,
      type: 'group',
      key: 'governance',
      children: [
        {
          name: 'نظرة عامة',
          href: ROUTES.GOVERNANCE,
        },
        {
          name: 'السياسات والإجراءات',
          href: ROUTES.POLICIES,
        },
        {
          name: 'إدارة الامتثال',
          href: ROUTES.COMPLIANCE,
        },
        {
          name: 'المراجعات والتدقيق',
          href: ROUTES.AUDITS,
        }
      ]
    },
    {
      name: 'التقارير والتحليلات',
      icon: ChartBarIcon,
      type: 'group',
      key: 'reports',
      children: [
        {
          name: 'لوحة التقارير',
          href: ROUTES.REPORTS_DASHBOARD,
        },
        {
          name: 'تقارير الحوادث',
          href: ROUTES.REPORTS_INCIDENTS,
        },
        {
          name: 'تقارير المخاطر',
          href: ROUTES.REPORTS_RISKS,
        },
        {
          name: 'تقارير الامتثال',
          href: ROUTES.REPORTS_COMPLIANCE,
        }
      ]
    }
  ]

  // إضافة إدارة المستخدمين بناءً على الصلاحيات
  if (hasPermission(user?.role, PERMISSIONS.USERS_VIEW)) {
    navigation.splice(1, 0, {
      name: 'إدارة المستخدمين',
      icon: UsersIcon,
      type: 'group',
      key: 'users',
      description: 'إدارة المستخدمين والصلاحيات',
      permission: PERMISSIONS.USERS_VIEW,
      children: [
        {
          name: 'جميع المستخدمين',
          href: ROUTES.USERS,
          description: 'عرض وإدارة جميع المستخدمين',
          permission: PERMISSIONS.USERS_VIEW
        },
        {
          name: 'إضافة مستخدم',
          href: ROUTES.USER_CREATE,
          description: 'إضافة مستخدم جديد للنظام',
          permission: PERMISSIONS.USERS_CREATE
        }
      ]
    })
  }

  const isCurrentPath = (path) => {
    return location.pathname === path
  }

  const isGroupActive = (group) => {
    if (!group.children) return false
    return group.children.some(child => location.pathname.startsWith(child.href))
  }

  const shouldExpandGroup = (groupKey) => {
    const group = navigation.find(nav => nav.key === groupKey)
    return expandedMenus[groupKey] || isGroupActive(group)
  }

  const NavigationItem = ({ item, isMobile = false }) => {
    // التحقق من الصلاحيات
    if (item.permission && !hasPermission(user?.role, item.permission)) {
      return null
    }

    if (item.type === 'single') {
      return (
        <Link
          to={item.href}
          onClick={isMobile ? () => setSidebarOpen(false) : undefined}
          className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            isCurrentPath(item.href)
              ? 'bg-primary-600 text-white'
              : 'text-dark-300 hover:bg-dark-700 hover:text-white'
          }`}
        >
          <item.icon className="ml-3 h-5 w-5" />
          {item.name}
        </Link>
      )
    }

    if (item.type === 'group') {
      const isExpanded = shouldExpandGroup(item.key)
      const isActive = isGroupActive(item)

      return (
        <div>
          <button
            onClick={() => toggleMenu(item.key)}
            className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isActive
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:bg-dark-700 hover:text-white'
            }`}
          >
            <div className="flex items-center">
              <item.icon className="ml-3 h-5 w-5" />
              {item.name}
            </div>
            {isExpanded ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </button>

          {isExpanded && (
            <div className="mr-6 mt-1 space-y-1">
              {item.children
                .filter(child => !child.permission || hasPermission(user?.role, child.permission))
                .map((child) => (
                  <Link
                    key={child.href}
                    to={child.href}
                    onClick={isMobile ? () => setSidebarOpen(false) : undefined}
                    className={`block px-3 py-2 rounded-lg text-sm transition-colors ${
                      isCurrentPath(child.href)
                        ? 'bg-primary-500 text-white'
                        : 'text-dark-400 hover:bg-dark-700 hover:text-white'
                    }`}
                  >
                    {child.name}
                  </Link>
                ))
              }
            </div>
          )}
        </div>
      )
    }

    return null
  }

  return (
    <div className="min-h-screen bg-dark-900">
      {/* الشريط الجانبي للهاتف المحمول */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-dark-900/80" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 w-64 bg-dark-800 border-l border-dark-700">
          <div className="flex items-center justify-between p-4 border-b border-dark-700">
            <h2 className="text-lg font-semibold text-white">القائمة</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-dark-400 hover:text-white"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="p-4 space-y-2">
            {navigation.map((item) => (
              <NavigationItem key={item.name} item={item} isMobile={true} />
            ))}
          </nav>
        </div>
      </div>

      {/* الشريط الجانبي للشاشات الكبيرة */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:w-64 lg:bg-dark-800 lg:border-l lg:border-dark-700">
        <div className="flex flex-col h-full">
          {/* الشعار */}
          <div className="flex items-center px-6 py-4 border-b border-dark-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-white" />
              </div>
              <div className="mr-3">
                <h1 className="text-sm font-semibold text-white">نظام GRC</h1>
                <p className="text-xs text-dark-400">إدارة المخاطر</p>
              </div>
            </div>
          </div>

          {/* التنقل */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavigationItem key={item.name} item={item} />
            ))}
          </nav>

          {/* معلومات المستخدم */}
          <div className="px-4 py-4 border-t border-dark-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center">
                <UserIcon className="h-5 w-5 text-dark-300" />
              </div>
              <div className="mr-3 flex-1">
                <p className="text-sm font-medium text-white">
                  {user?.full_name || user?.username}
                </p>
                <p className="text-xs text-dark-400">{user?.role === 'admin' ? 'مدير' : 'مستخدم'}</p>
              </div>
              <button
                onClick={onLogout}
                className="text-dark-400 hover:text-white"
                title="تسجيل الخروج"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="lg:mr-64">
        {/* الشريط العلوي */}
        <header className="bg-dark-800 border-b border-dark-700 px-4 py-3 lg:px-6">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-dark-400 hover:text-white"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-right">
                <p className="text-sm font-medium text-white">
                  {user?.full_name || user?.username}
                </p>
                <p className="text-xs text-dark-400">
                  {user?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>
              <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center lg:hidden">
                <UserIcon className="h-5 w-5 text-dark-300" />
              </div>
            </div>
          </div>
        </header>

        {/* المحتوى */}
        <main className="p-4 lg:p-6">
          <Breadcrumb />
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
