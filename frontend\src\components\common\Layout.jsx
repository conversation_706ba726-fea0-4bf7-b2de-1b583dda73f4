import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  HomeIcon, 
  ExclamationTriangleIcon, 
  PlusIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { ROUTES } from '../../utils/constants'

const Layout = ({ children, user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    {
      name: 'لوحة التحكم',
      href: ROUTES.DASHBOARD,
      icon: HomeIcon,
    },
    {
      name: 'الحوادث السيبرانية',
      href: ROUTES.INCIDENTS,
      icon: ExclamationTriangleIcon,
    },
    {
      name: 'إضافة حادث',
      href: ROUTES.INCIDENT_CREATE,
      icon: PlusIcon,
    },
  ]

  const isCurrentPath = (path) => {
    return location.pathname === path
  }

  return (
    <div className="min-h-screen bg-dark-900">
      {/* الشريط الجانبي للهاتف المحمول */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-dark-900/80" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 w-64 bg-dark-800 border-l border-dark-700">
          <div className="flex items-center justify-between p-4 border-b border-dark-700">
            <h2 className="text-lg font-semibold text-white">القائمة</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-dark-400 hover:text-white"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="p-4 space-y-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                onClick={() => setSidebarOpen(false)}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isCurrentPath(item.href)
                    ? 'bg-primary-600 text-white'
                    : 'text-dark-300 hover:bg-dark-700 hover:text-white'
                }`}
              >
                <item.icon className="ml-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* الشريط الجانبي للشاشات الكبيرة */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:w-64 lg:bg-dark-800 lg:border-l lg:border-dark-700">
        <div className="flex flex-col h-full">
          {/* الشعار */}
          <div className="flex items-center px-6 py-4 border-b border-dark-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-white" />
              </div>
              <div className="mr-3">
                <h1 className="text-sm font-semibold text-white">نظام GRC</h1>
                <p className="text-xs text-dark-400">إدارة المخاطر</p>
              </div>
            </div>
          </div>

          {/* التنقل */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isCurrentPath(item.href)
                    ? 'bg-primary-600 text-white'
                    : 'text-dark-300 hover:bg-dark-700 hover:text-white'
                }`}
              >
                <item.icon className="ml-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </nav>

          {/* معلومات المستخدم */}
          <div className="px-4 py-4 border-t border-dark-700">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center">
                <UserIcon className="h-5 w-5 text-dark-300" />
              </div>
              <div className="mr-3 flex-1">
                <p className="text-sm font-medium text-white">
                  {user?.full_name || user?.username}
                </p>
                <p className="text-xs text-dark-400">{user?.role === 'admin' ? 'مدير' : 'مستخدم'}</p>
              </div>
              <button
                onClick={onLogout}
                className="text-dark-400 hover:text-white"
                title="تسجيل الخروج"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="lg:mr-64">
        {/* الشريط العلوي */}
        <header className="bg-dark-800 border-b border-dark-700 px-4 py-3 lg:px-6">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-dark-400 hover:text-white"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-right">
                <p className="text-sm font-medium text-white">
                  {user?.full_name || user?.username}
                </p>
                <p className="text-xs text-dark-400">
                  {user?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>
              <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center lg:hidden">
                <UserIcon className="h-5 w-5 text-dark-300" />
              </div>
            </div>
          </div>
        </header>

        {/* المحتوى */}
        <main className="p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
