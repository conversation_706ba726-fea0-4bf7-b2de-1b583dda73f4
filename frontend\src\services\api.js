import axios from 'axios'
import toast from 'react-hot-toast'
import { API_BASE_URL } from '../utils/constants'

// إنشاء مثيل axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// إضافة رمز المصادقة تلقائياً
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// معالجة الاستجابات والأخطاء
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error

    if (response) {
      switch (response.status) {
        case 401:
          // إزالة الرمز المميز وإعادة التوجيه لصفحة تسجيل الدخول
          localStorage.removeItem('access_token')
          localStorage.removeItem('user')
          window.location.href = '/login'
          toast.error('انتهت صلاحية جلستك، يرجى تسجيل الدخول مرة أخرى')
          break
        case 403:
          toast.error('ليس لديك صلاحية للوصول إلى هذا المورد')
          break
        case 404:
          toast.error('المورد المطلوب غير موجود')
          break
        case 422:
          // أخطاء التحقق
          if (response.data.detail) {
            if (Array.isArray(response.data.detail)) {
              response.data.detail.forEach(error => {
                toast.error(error.msg || 'خطأ في التحقق من البيانات')
              })
            } else {
              toast.error(response.data.detail)
            }
          }
          break
        case 500:
          toast.error('خطأ داخلي في الخادم')
          break
        default:
          toast.error(response.data.detail || 'حدث خطأ غير متوقع')
      }
    } else if (error.request) {
      toast.error('فشل في الاتصال بالخادم')
    } else {
      toast.error('حدث خطأ غير متوقع')
    }

    return Promise.reject(error)
  }
)

// وظائف API العامة
export const apiService = {
  // GET request
  get: async (url, config = {}) => {
    try {
      const response = await api.get(url, config)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // POST request
  post: async (url, data = {}, config = {}) => {
    try {
      const response = await api.post(url, data, config)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // PUT request
  put: async (url, data = {}, config = {}) => {
    try {
      const response = await api.put(url, data, config)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // DELETE request
  delete: async (url, config = {}) => {
    try {
      const response = await api.delete(url, config)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // PATCH request
  patch: async (url, data = {}, config = {}) => {
    try {
      const response = await api.patch(url, data, config)
      return response.data
    } catch (error) {
      throw error
    }
  },
}

// وظائف مساعدة للمصادقة
export const authUtils = {
  // حفظ الرمز المميز
  setToken: (token) => {
    localStorage.setItem('access_token', token)
  },

  // الحصول على الرمز المميز
  getToken: () => {
    return localStorage.getItem('access_token')
  },

  // إزالة الرمز المميز
  removeToken: () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user')
  },

  // التحقق من وجود رمز مميز صالح
  isAuthenticated: () => {
    const token = localStorage.getItem('access_token')
    return !!token
  },

  // حفظ بيانات المستخدم
  setUser: (user) => {
    localStorage.setItem('user', JSON.stringify(user))
  },

  // الحصول على بيانات المستخدم
  getUser: () => {
    const user = localStorage.getItem('user')
    return user ? JSON.parse(user) : null
  },
}

export default api
