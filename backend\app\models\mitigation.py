from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, ForeignKey, Float, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum


class MitigationStrategy(str, enum.Enum):
    """استراتيجيات التخفيف"""
    AVOID = "avoid"  # تجنب
    MITIGATE = "mitigate"  # تخفيف
    TRANSFER = "transfer"  # نقل
    ACCEPT = "accept"  # قبول
    MONITOR = "monitor"  # مراقبة


class ActionStatus(str, enum.Enum):
    """حالات الإجراءات"""
    PLANNED = "planned"  # مخطط
    IN_PROGRESS = "in_progress"  # قيد التنفيذ
    COMPLETED = "completed"  # مكتمل
    OVERDUE = "overdue"  # متأخر
    CANCELLED = "cancelled"  # ملغي
    ON_HOLD = "on_hold"  # معلق


class Priority(str, enum.Enum):
    """أولويات الإجراءات"""
    LOW = "low"  # منخفضة
    MEDIUM = "medium"  # متوسطة
    HIGH = "high"  # عالية
    CRITICAL = "critical"  # حرجة


class MitigationPlan(Base):
    """خطط التخفيف"""
    __tablename__ = "mitigation_plans"

    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    strategy = Column(Enum(MitigationStrategy), nullable=False)
    
    # الأهداف والمؤشرات
    objective = Column(Text)  # الهدف من الخطة
    success_criteria = Column(Text)  # معايير النجاح
    target_risk_reduction = Column(Float)  # نسبة تقليل المخاطر المستهدفة
    
    # التكلفة والموارد
    estimated_cost = Column(Float)  # التكلفة المقدرة
    budget_approved = Column(Boolean, default=False)
    resources_required = Column(Text)  # الموارد المطلوبة
    
    # المسؤوليات
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    sponsor_id = Column(Integer, ForeignKey("users.id"))  # الراعي
    
    # التواريخ
    start_date = Column(DateTime(timezone=True))
    target_completion_date = Column(DateTime(timezone=True))
    actual_completion_date = Column(DateTime(timezone=True))
    
    # الحالة والتقدم
    status = Column(String(50), default="draft")  # draft, approved, active, completed, cancelled
    progress_percentage = Column(Float, default=0.0)
    
    # المراجعة والموافقة
    approved_by_id = Column(Integer, ForeignKey("users.id"))
    approval_date = Column(DateTime(timezone=True))
    last_review_date = Column(DateTime(timezone=True))
    next_review_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # العلاقات
    risk = relationship("Risk", back_populates="mitigation_plans")
    owner = relationship("User", foreign_keys=[owner_id])
    sponsor = relationship("User", foreign_keys=[sponsor_id])
    approved_by = relationship("User", foreign_keys=[approved_by_id])
    created_by = relationship("User", foreign_keys=[created_by_id])
    
    actions = relationship("MitigationAction", back_populates="plan", cascade="all, delete-orphan")
    reviews = relationship("PlanReview", back_populates="plan", cascade="all, delete-orphan")

    def calculate_progress(self):
        """حساب نسبة التقدم بناءً على الإجراءات"""
        if not self.actions:
            return 0.0
        
        total_actions = len(self.actions)
        completed_actions = len([a for a in self.actions if a.status == ActionStatus.COMPLETED])
        
        self.progress_percentage = (completed_actions / total_actions) * 100
        return self.progress_percentage

    def __repr__(self):
        return f"<MitigationPlan(id={self.id}, title='{self.title}')>"


class MitigationAction(Base):
    """إجراءات التخفيف"""
    __tablename__ = "mitigation_actions"

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey("mitigation_plans.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    
    # التفاصيل
    action_type = Column(String(50))  # preventive, corrective, detective
    priority = Column(Enum(Priority), default=Priority.MEDIUM)
    
    # المسؤوليات
    assigned_to_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    reviewer_id = Column(Integer, ForeignKey("users.id"))
    
    # التواريخ والمواعيد
    start_date = Column(DateTime(timezone=True))
    due_date = Column(DateTime(timezone=True), nullable=False)
    completion_date = Column(DateTime(timezone=True))
    
    # الحالة والتقدم
    status = Column(Enum(ActionStatus), default=ActionStatus.PLANNED)
    progress_notes = Column(Text)
    completion_evidence = Column(Text)  # أدلة الإنجاز
    
    # التكلفة والموارد
    estimated_effort_hours = Column(Float)
    actual_effort_hours = Column(Float)
    cost = Column(Float)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    plan = relationship("MitigationPlan", back_populates="actions")
    assigned_to = relationship("User", foreign_keys=[assigned_to_id])
    reviewer = relationship("User", foreign_keys=[reviewer_id])
    
    updates = relationship("ActionUpdate", back_populates="action", cascade="all, delete-orphan")

    def is_overdue(self):
        """التحقق من تأخر الإجراء"""
        if self.status in [ActionStatus.COMPLETED, ActionStatus.CANCELLED]:
            return False
        
        from datetime import datetime, timezone
        return self.due_date < datetime.now(timezone.utc)

    def __repr__(self):
        return f"<MitigationAction(id={self.id}, title='{self.title}')>"


class ActionUpdate(Base):
    """تحديثات الإجراءات"""
    __tablename__ = "action_updates"

    id = Column(Integer, primary_key=True, index=True)
    action_id = Column(Integer, ForeignKey("mitigation_actions.id"), nullable=False)
    
    update_text = Column(Text, nullable=False)
    progress_percentage = Column(Float)
    
    # المرفقات والأدلة
    attachments = Column(Text)  # JSON array of file paths
    
    # المسؤوليات
    updated_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # العلاقات
    action = relationship("MitigationAction", back_populates="updates")
    updated_by = relationship("User")

    def __repr__(self):
        return f"<ActionUpdate(id={self.id}, action_id={self.action_id})>"


class PlanReview(Base):
    """مراجعات خطط التخفيف"""
    __tablename__ = "plan_reviews"

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey("mitigation_plans.id"), nullable=False)
    
    review_date = Column(DateTime(timezone=True), nullable=False)
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # نتائج المراجعة
    effectiveness_rating = Column(Integer)  # 1-5
    progress_assessment = Column(Text)
    issues_identified = Column(Text)
    recommendations = Column(Text)
    
    # القرارات
    continue_plan = Column(Boolean, default=True)
    modify_plan = Column(Boolean, default=False)
    escalate = Column(Boolean, default=False)
    
    # التواريخ التالية
    next_review_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # العلاقات
    plan = relationship("MitigationPlan", back_populates="reviews")
    reviewer = relationship("User")

    def __repr__(self):
        return f"<PlanReview(id={self.id}, plan_id={self.plan_id})>"


class RiskTreatment(Base):
    """معالجة المخاطر الشاملة"""
    __tablename__ = "risk_treatments"

    id = Column(Integer, primary_key=True, index=True)
    risk_id = Column(Integer, ForeignKey("risks.id"), nullable=False)
    
    treatment_strategy = Column(Enum(MitigationStrategy), nullable=False)
    justification = Column(Text)  # مبرر الاستراتيجية
    
    # التقييم قبل وبعد المعالجة
    residual_probability = Column(Integer)  # الاحتمالية المتبقية
    residual_impact = Column(Integer)  # التأثير المتبقي
    residual_risk_score = Column(Float)  # النتيجة المتبقية
    
    # الموافقة والمسؤوليات
    approved_by_id = Column(Integer, ForeignKey("users.id"))
    approval_date = Column(DateTime(timezone=True))
    
    # التواريخ
    implementation_date = Column(DateTime(timezone=True))
    review_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    risk = relationship("Risk")
    approved_by = relationship("User")

    def __repr__(self):
        return f"<RiskTreatment(id={self.id}, strategy='{self.treatment_strategy}')>"
