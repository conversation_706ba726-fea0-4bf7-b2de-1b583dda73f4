# هيكل مشروع نظام إدارة الحوكمة والمخاطر والامتثال (GRC)

```
grc-system/
├── backend/                    # خادم FastAPI
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # نقطة دخول التطبيق
│   │   ├── config.py          # إعدادات التطبيق
│   │   ├── database.py        # إعداد قاعدة البيانات
│   │   ├── models/            # نماذج قاعدة البيانات
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── incident.py
│   │   │   └── log.py
│   │   ├── schemas/           # Pydantic schemas
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── incident.py
│   │   │   └── auth.py
│   │   ├── api/               # API endpoints
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── users.py
│   │   │   └── incidents.py
│   │   ├── core/              # الوظائف الأساسية
│   │   │   ├── __init__.py
│   │   │   ├── security.py    # JWT & password hashing
│   │   │   └── deps.py        # Dependencies
│   │   └── utils/             # أدوات مساعدة
│   │       ├── __init__.py
│   │       └── helpers.py
│   ├── requirements.txt       # مكتبات Python
│   ├── .env                   # متغيرات البيئة
│   └── run.py                 # ملف تشغيل الخادم
├── frontend/                  # تطبيق React
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/        # مكونات React
│   │   │   ├── common/        # مكونات مشتركة
│   │   │   ├── auth/          # مكونات المصادقة
│   │   │   ├── dashboard/     # مكونات لوحة التحكم
│   │   │   └── incidents/     # مكونات الحوادث
│   │   ├── pages/             # صفحات التطبيق
│   │   │   ├── Login.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── IncidentForm.jsx
│   │   │   └── IncidentList.jsx
│   │   ├── services/          # خدمات API
│   │   │   ├── api.js
│   │   │   ├── auth.js
│   │   │   └── incidents.js
│   │   ├── utils/             # أدوات مساعدة
│   │   │   ├── constants.js
│   │   │   └── helpers.js
│   │   ├── styles/            # ملفات CSS
│   │   │   └── globals.css
│   │   ├── App.jsx            # المكون الرئيسي
│   │   └── index.js           # نقطة دخول React
│   ├── package.json           # مكتبات Node.js
│   ├── tailwind.config.js     # إعدادات TailwindCSS
│   └── vite.config.js         # إعدادات Vite
├── database/                  # ملفات قاعدة البيانات
│   └── grc_system.db         # قاعدة بيانات SQLite
├── docs/                      # التوثيق
│   ├── api_documentation.md
│   └── user_guide.md
├── README.md                  # دليل المشروع
└── .gitignore                # ملفات Git المتجاهلة
```

## المكتبات المطلوبة:

### Backend (Python):
- fastapi
- uvicorn
- sqlalchemy
- sqlite3
- pydantic
- python-jose[cryptography]
- passlib[bcrypt]
- python-multipart
- python-dotenv

### Frontend (Node.js):
- react
- react-dom
- react-router-dom
- axios
- tailwindcss
- @headlessui/react
- @heroicons/react
- vite
