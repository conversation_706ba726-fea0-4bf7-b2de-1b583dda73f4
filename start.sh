#!/bin/bash

echo "========================================"
echo "  نظام إدارة الحوكمة والمخاطر والامتثال"
echo "========================================"
echo

echo "تشغيل Backend..."
cd backend
python run.py &
BACKEND_PID=$!
cd ..

echo "انتظار 5 ثوان لتشغيل Backend..."
sleep 5

echo "تشغيل Frontend..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo
echo "تم تشغيل النظام بنجاح!"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:3000"
echo
echo "بيانات المدير الافتراضي:"
echo "اسم المستخدم: admin"
echo "كلمة المرور: Admin@123"
echo
echo "اضغط Ctrl+C لإيقاف النظام"

# انتظار إشارة الإيقاف
trap "echo 'إيقاف النظام...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT

# انتظار العمليات
wait
