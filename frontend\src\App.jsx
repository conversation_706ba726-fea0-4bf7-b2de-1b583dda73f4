import React, { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { authService } from './services/auth'
import { authUtils } from './services/api'
import { ROUTES } from './utils/constants'

// استيراد الصفحات
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import IncidentForm from './pages/IncidentForm'
import IncidentList from './pages/IncidentList'
import Users from './pages/Users'
import CreateUser from './pages/CreateUser'
import EditUser from './pages/EditUser'
import UserDetails from './pages/UserDetails'

// استيراد المكونات
import Layout from './components/common/Layout'
import LoadingSpinner from './components/common/LoadingSpinner'
import ProtectedRoute from './components/auth/ProtectedRoute'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState(null)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const isAuth = await authService.checkAuth()
      setIsAuthenticated(isAuth)
      
      if (isAuth) {
        const userData = authUtils.getUser()
        setUser(userData)
      }
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error)
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogin = (userData) => {
    setIsAuthenticated(true)
    setUser(userData)
  }

  const handleLogout = () => {
    authService.logout()
    setIsAuthenticated(false)
    setUser(null)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-dark-900 text-white">
      <Routes>
        {/* صفحة تسجيل الدخول */}
        <Route
          path={ROUTES.LOGIN}
          element={
            isAuthenticated ? (
              <Navigate to={ROUTES.DASHBOARD} replace />
            ) : (
              <Login onLogin={handleLogin} />
            )
          }
        />

        {/* المسارات المحمية */}
        <Route
          path="/*"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <Layout user={user} onLogout={handleLogout}>
                <Routes>
                  {/* الصفحة الرئيسية */}
                  <Route
                    path={ROUTES.HOME}
                    element={<Navigate to={ROUTES.DASHBOARD} replace />}
                  />

                  {/* لوحة التحكم */}
                  <Route
                    path={ROUTES.DASHBOARD}
                    element={<Dashboard user={user} />}
                  />

                  {/* قائمة الحوادث */}
                  <Route
                    path={ROUTES.INCIDENTS}
                    element={<IncidentList user={user} />}
                  />

                  {/* إنشاء حادث جديد */}
                  <Route
                    path={ROUTES.INCIDENT_CREATE}
                    element={<IncidentForm user={user} />}
                  />

                  {/* تعديل حادث */}
                  <Route
                    path="/incidents/edit/:id"
                    element={<IncidentForm user={user} isEdit />}
                  />

                  {/* عرض تفاصيل حادث */}
                  <Route
                    path="/incidents/:id"
                    element={<IncidentList user={user} viewMode />}
                  />

                  {/* إدارة المستخدمين - للمديرين فقط */}
                  <Route
                    path={ROUTES.USERS}
                    element={
                      user?.role === 'ADMIN' ? (
                        <Users user={user} />
                      ) : (
                        <Navigate to={ROUTES.DASHBOARD} replace />
                      )
                    }
                  />

                  {/* إضافة مستخدم جديد - للمديرين فقط */}
                  <Route
                    path={ROUTES.USER_CREATE}
                    element={
                      user?.role === 'ADMIN' ? (
                        <CreateUser user={user} />
                      ) : (
                        <Navigate to={ROUTES.DASHBOARD} replace />
                      )
                    }
                  />

                  {/* تعديل مستخدم - للمديرين فقط */}
                  <Route
                    path="/users/edit/:id"
                    element={
                      user?.role === 'ADMIN' ? (
                        <EditUser user={user} />
                      ) : (
                        <Navigate to={ROUTES.DASHBOARD} replace />
                      )
                    }
                  />

                  {/* عرض تفاصيل مستخدم - للمديرين فقط */}
                  <Route
                    path="/users/:id"
                    element={
                      user?.role === 'ADMIN' ? (
                        <UserDetails user={user} />
                      ) : (
                        <Navigate to={ROUTES.DASHBOARD} replace />
                      )
                    }
                  />

                  {/* مسار افتراضي */}
                  <Route
                    path="/users/edit/:id"
                    element={
                      user?.role === 'ADMIN' ? (
                        <CreateUser user={user} isEdit />
                      ) : (
                        <Navigate to={ROUTES.DASHBOARD} replace />
                      )
                    }
                  />

                  {/* صفحة غير موجودة */}
                  <Route
                    path="*"
                    element={
                      <div className="flex items-center justify-center min-h-96">
                        <div className="text-center">
                          <h1 className="text-4xl font-bold text-dark-300 mb-4">404</h1>
                          <p className="text-dark-400 mb-6">الصفحة غير موجودة</p>
                          <button
                            onClick={() => window.history.back()}
                            className="btn-primary"
                          >
                            العودة
                          </button>
                        </div>
                      </div>
                    }
                  />
                </Routes>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

export default App
