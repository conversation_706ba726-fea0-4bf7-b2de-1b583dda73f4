"""
نموذج الحوادث السيبرانية
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class IncidentType(str, enum.Enum):
    """أنواع الحوادث السيبرانية"""
    MALWARE = "malware"                    # برمجيات خبيثة
    PHISHING = "phishing"                  # تصيد إلكتروني
    DATA_BREACH = "data_breach"            # تسريب بيانات
    UNAUTHORIZED_ACCESS = "unauthorized_access"  # وصول غير مصرح
    DENIAL_OF_SERVICE = "denial_of_service"      # حرمان من الخدمة
    INSIDER_THREAT = "insider_threat"      # تهديد داخلي
    RANSOMWARE = "ransomware"              # برمجيات الفدية
    SOCIAL_ENGINEERING = "social_engineering"   # هندسة اجتماعية
    OTHER = "other"                        # أخرى


class IncidentSeverity(str, enum.Enum):
    """مستوى خطورة الحادث"""
    LOW = "low"        # منخفض
    MEDIUM = "medium"  # متوسط
    HIGH = "high"      # عالي
    CRITICAL = "critical"  # حرج


class IncidentStatus(str, enum.Enum):
    """حالة الحادث"""
    OPEN = "open"              # مفتوح
    IN_PROGRESS = "in_progress"  # قيد المعالجة
    RESOLVED = "resolved"      # محلول
    CLOSED = "closed"          # مغلق


class Incident(Base):
    """نموذج الحادث السيبراني"""
    __tablename__ = "incidents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    incident_type = Column(Enum(IncidentType), nullable=False)
    severity = Column(Enum(IncidentSeverity), default=IncidentSeverity.MEDIUM)
    status = Column(Enum(IncidentStatus), default=IncidentStatus.OPEN)
    
    # تواريخ مهمة
    incident_date = Column(DateTime(timezone=True), nullable=False)
    reported_date = Column(DateTime(timezone=True), server_default=func.now())
    resolved_date = Column(DateTime(timezone=True), nullable=True)
    
    # تفاصيل إضافية
    affected_systems = Column(Text, nullable=True)  # الأنظمة المتأثرة
    impact_description = Column(Text, nullable=True)  # وصف التأثير
    actions_taken = Column(Text, nullable=True)      # الإجراءات المتخذة
    lessons_learned = Column(Text, nullable=True)    # الدروس المستفادة
    
    # المراجع الخارجية
    reference_number = Column(String(50), unique=True, nullable=True)  # رقم مرجعي
    nca_reported = Column(String(10), default="لا")  # هل تم الإبلاغ لهيئة الاتصالات
    
    # العلاقات
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_by_user = relationship("User", back_populates="incidents")
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Incident(title='{self.title}', type='{self.incident_type}', status='{self.status}')>"
