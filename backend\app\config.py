"""
إعدادات التطبيق الرئيسية
"""
import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """إعدادات التطبيق"""
    
    # إعدادات التطبيق الأساسية
    app_name: str = "نظام إدارة الحوكمة والمخاطر والامتثال"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # إعدادات قاعدة البيانات
    database_url: str = "sqlite:///./database/grc_system.db"
    
    # إعدادات الحماية
    secret_key: str = "your-secret-key-here-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # إعدادات CORS
    allowed_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# إنشاء مثيل من الإعدادات
settings = Settings()
