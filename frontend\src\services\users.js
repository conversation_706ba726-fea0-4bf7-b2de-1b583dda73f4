import { API_BASE_URL } from '../utils/constants'

class UsersService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/users`
  }

  // الحصول على token من localStorage
  getAuthHeaders() {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  }

  // الحصول على قائمة المستخدمين
  async getUsers(params = {}) {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page)
    if (params.per_page) queryParams.append('per_page', params.per_page)
    if (params.search) queryParams.append('search', params.search)
    if (params.role) queryParams.append('role', params.role)
    if (params.is_active !== undefined) queryParams.append('is_active', params.is_active)

    const url = `${this.baseURL}/?${queryParams.toString()}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحميل المستخدمين')
    }

    return await response.json()
  }

  // الحصول على مستخدم محدد
  async getUser(userId) {
    const response = await fetch(`${this.baseURL}/${userId}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحميل المستخدم')
    }

    return await response.json()
  }

  // إنشاء مستخدم جديد
  async createUser(userData) {
    try {
      const response = await fetch(this.baseURL, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.detail || errorData.message || `خطأ HTTP ${response.status}`
        throw new Error(errorMessage)
      }

      return await response.json()
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('فشل في الاتصال بالخادم. تأكد من أن الخادم يعمل.')
      }
      throw error
    }
  }

  // تحديث مستخدم
  async updateUser(userId, userData) {
    const response = await fetch(`${this.baseURL}/${userId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحديث المستخدم')
    }

    return await response.json()
  }

  // حذف مستخدم
  async deleteUser(userId) {
    const response = await fetch(`${this.baseURL}/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في حذف المستخدم')
    }

    return true
  }

  // تغيير كلمة المرور
  async changePassword(userId, passwordData) {
    const response = await fetch(`${this.baseURL}/${userId}/change-password`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(passwordData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تغيير كلمة المرور')
    }

    return await response.json()
  }

  // الحصول على الملف الشخصي
  async getProfile() {
    const response = await fetch(`${this.baseURL}/profile/me`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحميل الملف الشخصي')
    }

    return await response.json()
  }

  // تحديث الملف الشخصي
  async updateProfile(profileData) {
    const response = await fetch(`${this.baseURL}/profile/me`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحديث الملف الشخصي')
    }

    return await response.json()
  }

  // تفعيل/إلغاء تفعيل المستخدم
  async toggleUserStatus(userId) {
    const response = await fetch(`${this.baseURL}/${userId}/toggle-status`, {
      method: 'PUT',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تغيير حالة المستخدم')
    }

    return await response.json()
  }

  // إحصائيات المستخدمين
  async getUsersStats() {
    const response = await fetch(`${this.baseURL}/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحميل إحصائيات المستخدمين')
    }

    return await response.json()
  }

  // البحث في المستخدمين
  async searchUsers(searchTerm, filters = {}) {
    const params = {
      search: searchTerm,
      ...filters
    }
    
    return await this.getUsers(params)
  }

  // تصدير قائمة المستخدمين
  async exportUsers(format = 'csv') {
    const response = await fetch(`${this.baseURL}/export?format=${format}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تصدير المستخدمين')
    }

    return await response.blob()
  }

  // التحقق من توفر اسم المستخدم
  async checkUsernameAvailability(username) {
    const response = await fetch(`${this.baseURL}/check-username?username=${encodeURIComponent(username)}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في التحقق من اسم المستخدم')
    }

    return await response.json()
  }

  // التحقق من توفر البريد الإلكتروني
  async checkEmailAvailability(email) {
    const response = await fetch(`${this.baseURL}/check-email?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في التحقق من البريد الإلكتروني')
    }

    return await response.json()
  }

  // إعادة تعيين كلمة المرور (للمديرين)
  async resetUserPassword(userId, newPassword) {
    const response = await fetch(`${this.baseURL}/${userId}/reset-password`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ new_password: newPassword })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في إعادة تعيين كلمة المرور')
    }

    return await response.json()
  }

  // الحصول على سجل نشاط المستخدم
  async getUserActivity(userId, params = {}) {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page)
    if (params.per_page) queryParams.append('per_page', params.per_page)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)

    const url = `${this.baseURL}/${userId}/activity?${queryParams.toString()}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'حدث خطأ في تحميل نشاط المستخدم')
    }

    return await response.json()
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const usersService = new UsersService()
export default usersService
