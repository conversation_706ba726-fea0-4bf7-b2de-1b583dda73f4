from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum


class RiskType(str, Enum):
    """أنواع المخاطر"""
    OPERATIONAL = "operational"
    FINANCIAL = "financial"
    STRATEGIC = "strategic"
    COMPLIANCE = "compliance"
    TECHNOLOGY = "technology"
    REPUTATION = "reputation"
    SECURITY = "security"
    ENVIRONMENTAL = "environmental"
    LEGAL = "legal"
    HUMAN_RESOURCES = "human_resources"


class RiskStatus(str, Enum):
    """حالات المخاطر"""
    IDENTIFIED = "identified"
    ASSESSED = "assessed"
    MITIGATED = "mitigated"
    MONITORED = "monitored"
    CLOSED = "closed"
    ESCALATED = "escalated"


class RiskLevel(str, Enum):
    """مستويات المخاطر"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


# Base schemas
class RiskBase(BaseModel):
    """المخطط الأساسي للمخاطر"""
    title: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    risk_type: RiskType
    probability: int = Field(..., ge=1, le=5)
    impact: int = Field(..., ge=1, le=5)
    business_unit: Optional[str] = Field(None, max_length=100)
    category: Optional[str] = Field(None, max_length=100)
    source: Optional[str] = Field(None, max_length=100)
    next_review_date: Optional[datetime] = None

    @validator('probability', 'impact')
    def validate_score(cls, v):
        if v < 1 or v > 5:
            raise ValueError('يجب أن تكون القيمة بين 1 و 5')
        return v


class RiskCreate(RiskBase):
    """مخطط إنشاء المخاطر"""
    owner_id: int = Field(..., gt=0)


class RiskUpdate(BaseModel):
    """مخطط تحديث المخاطر"""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1)
    risk_type: Optional[RiskType] = None
    probability: Optional[int] = Field(None, ge=1, le=5)
    impact: Optional[int] = Field(None, ge=1, le=5)
    status: Optional[RiskStatus] = None
    business_unit: Optional[str] = Field(None, max_length=100)
    category: Optional[str] = Field(None, max_length=100)
    source: Optional[str] = Field(None, max_length=100)
    next_review_date: Optional[datetime] = None
    owner_id: Optional[int] = Field(None, gt=0)

    @validator('probability', 'impact')
    def validate_score(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('يجب أن تكون القيمة بين 1 و 5')
        return v


class UserBasic(BaseModel):
    """معلومات المستخدم الأساسية"""
    id: int
    username: str
    full_name: Optional[str]
    email: str

    class Config:
        from_attributes = True


class RiskResponse(RiskBase):
    """استجابة المخاطر"""
    id: int
    risk_score: float
    risk_level: RiskLevel
    status: RiskStatus
    is_active: bool
    identified_date: datetime
    last_review_date: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    
    # معلومات المستخدمين
    owner: UserBasic
    created_by: UserBasic
    updated_by: Optional[UserBasic]

    class Config:
        from_attributes = True


class RiskListResponse(BaseModel):
    """استجابة قائمة المخاطر"""
    id: int
    title: str
    risk_type: RiskType
    risk_level: RiskLevel
    risk_score: float
    status: RiskStatus
    owner: UserBasic
    identified_date: datetime
    next_review_date: Optional[datetime]

    class Config:
        from_attributes = True


class RiskSummary(BaseModel):
    """ملخص المخاطر"""
    total_risks: int
    by_level: dict
    by_type: dict
    by_status: dict
    overdue_reviews: int


# Risk Assessment schemas
class RiskAssessmentBase(BaseModel):
    """المخطط الأساسي لتقييم المخاطر"""
    probability: int = Field(..., ge=1, le=5)
    impact: int = Field(..., ge=1, le=5)
    assessment_notes: Optional[str] = None
    methodology: Optional[str] = Field(None, max_length=100)


class RiskAssessmentCreate(RiskAssessmentBase):
    """مخطط إنشاء تقييم المخاطر"""
    risk_id: int = Field(..., gt=0)


class RiskAssessmentResponse(RiskAssessmentBase):
    """استجابة تقييم المخاطر"""
    id: int
    risk_id: int
    risk_score: float
    risk_level: RiskLevel
    assessment_date: datetime
    assessor: UserBasic
    approved_by: Optional[UserBasic]
    approval_date: Optional[datetime]

    class Config:
        from_attributes = True


# Risk Control schemas
class RiskControlBase(BaseModel):
    """المخطط الأساسي لضوابط المخاطر"""
    title: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    control_type: str = Field(..., max_length=50)
    effectiveness: Optional[int] = Field(None, ge=1, le=5)
    implementation_status: Optional[str] = Field(None, max_length=50)


class RiskControlCreate(RiskControlBase):
    """مخطط إنشاء ضابطة المخاطر"""
    risk_id: int = Field(..., gt=0)
    owner_id: int = Field(..., gt=0)


class RiskControlResponse(RiskControlBase):
    """استجابة ضابطة المخاطر"""
    id: int
    risk_id: int
    owner: UserBasic
    implementation_date: Optional[datetime]
    last_test_date: Optional[datetime]
    next_test_date: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


# Risk Incident schemas
class RiskIncidentBase(BaseModel):
    """المخطط الأساسي لحوادث المخاطر"""
    title: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    incident_date: datetime
    severity: str = Field(..., max_length=50)
    impact_description: Optional[str] = None
    response_actions: Optional[str] = None
    lessons_learned: Optional[str] = None


class RiskIncidentCreate(RiskIncidentBase):
    """مخطط إنشاء حادث المخاطر"""
    risk_id: int = Field(..., gt=0)
    assigned_to_id: Optional[int] = Field(None, gt=0)


class RiskIncidentResponse(RiskIncidentBase):
    """استجابة حادث المخاطر"""
    id: int
    risk_id: int
    status: str
    reported_by: UserBasic
    assigned_to: Optional[UserBasic]
    created_at: datetime

    class Config:
        from_attributes = True


# Risk Matrix schemas
class RiskMatrixCell(BaseModel):
    """خلية في مصفوفة المخاطر"""
    probability: int
    impact: int
    risk_level: RiskLevel
    risk_count: int
    risks: List[RiskListResponse]


class RiskMatrix(BaseModel):
    """مصفوفة المخاطر"""
    matrix: List[List[RiskMatrixCell]]
    summary: RiskSummary


# Filter schemas
class RiskFilter(BaseModel):
    """مرشحات المخاطر"""
    risk_type: Optional[RiskType] = None
    risk_level: Optional[RiskLevel] = None
    status: Optional[RiskStatus] = None
    owner_id: Optional[int] = None
    business_unit: Optional[str] = None
    category: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    overdue_only: Optional[bool] = False


class RiskSearchParams(BaseModel):
    """معاملات البحث في المخاطر"""
    search: Optional[str] = None
    page: int = Field(1, ge=1)
    per_page: int = Field(10, ge=1, le=100)
    sort_by: Optional[str] = "created_at"
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$")
    filters: Optional[RiskFilter] = None
