#!/bin/bash

echo "========================================"
echo "  تثبيت نظام إدارة الحوكمة والمخاطر والامتثال"
echo "========================================"
echo

echo "تثبيت مكتبات Backend..."
cd backend

echo "إنشاء البيئة الافتراضية..."
python3 -m venv venv

echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

echo "تثبيت المكتبات المطلوبة..."
pip install -r requirements.txt

echo "تم تثبيت Backend بنجاح!"
cd ..

echo
echo "تثبيت مكتبات Frontend..."
cd frontend

echo "تثبيت Node.js packages..."
npm install

echo "تم تثبيت Frontend بنجاح!"
cd ..

echo
echo "========================================"
echo "تم تثبيت النظام بنجاح!"
echo "========================================"
echo
echo "لتشغيل النظام، استخدم:"
echo "./start.sh"
echo
echo "أو قم بتشغيل كل جزء منفصلاً:"
echo "Backend: cd backend && python run.py"
echo "Frontend: cd frontend && npm run dev"
echo

# جعل ملف التشغيل قابل للتنفيذ
chmod +x start.sh
