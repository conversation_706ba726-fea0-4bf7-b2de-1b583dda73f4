from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.risk import Risk, RiskAssessment, RiskControl, RiskIncident, RiskLevel, RiskStatus, RiskType
from app.models.user import User
from app.schemas.risk import RiskCreate, RiskUpdate, RiskFilter, RiskSearchParams


def get_risk(db: Session, risk_id: int) -> Optional[Risk]:
    """الحصول على مخاطرة بالمعرف"""
    return db.query(Risk).options(
        joinedload(Risk.owner),
        joinedload(Risk.created_by),
        joinedload(Risk.updated_by)
    ).filter(Risk.id == risk_id).first()


def get_risks(
    db: Session, 
    search_params: RiskSearchParams,
    current_user: User
) -> tuple[List[Risk], int]:
    """الحصول على قائمة المخاطر مع الفلترة والبحث"""
    query = db.query(Risk).options(
        joinedload(Risk.owner),
        joinedload(Risk.created_by)
    )
    
    # تطبيق الفلاتر
    if search_params.filters:
        query = apply_risk_filters(query, search_params.filters)
    
    # البحث النصي
    if search_params.search:
        search_term = f"%{search_params.search}%"
        query = query.filter(
            or_(
                Risk.title.ilike(search_term),
                Risk.description.ilike(search_term),
                Risk.business_unit.ilike(search_term),
                Risk.category.ilike(search_term)
            )
        )
    
    # فلترة حسب الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(Risk.owner_id == current_user.id)
    
    # العد الإجمالي
    total = query.count()
    
    # الترتيب
    if search_params.sort_by:
        order_column = getattr(Risk, search_params.sort_by, Risk.created_at)
        if search_params.sort_order == "desc":
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))
    
    # التصفح
    offset = (search_params.page - 1) * search_params.per_page
    risks = query.offset(offset).limit(search_params.per_page).all()
    
    return risks, total


def apply_risk_filters(query, filters: RiskFilter):
    """تطبيق فلاتر المخاطر"""
    if filters.risk_type:
        query = query.filter(Risk.risk_type == filters.risk_type)
    
    if filters.risk_level:
        query = query.filter(Risk.risk_level == filters.risk_level)
    
    if filters.status:
        query = query.filter(Risk.status == filters.status)
    
    if filters.owner_id:
        query = query.filter(Risk.owner_id == filters.owner_id)
    
    if filters.business_unit:
        query = query.filter(Risk.business_unit.ilike(f"%{filters.business_unit}%"))
    
    if filters.category:
        query = query.filter(Risk.category.ilike(f"%{filters.category}%"))
    
    if filters.date_from:
        query = query.filter(Risk.identified_date >= filters.date_from)
    
    if filters.date_to:
        query = query.filter(Risk.identified_date <= filters.date_to)
    
    if filters.overdue_only:
        query = query.filter(
            and_(
                Risk.next_review_date.isnot(None),
                Risk.next_review_date < datetime.utcnow()
            )
        )
    
    return query


def create_risk(db: Session, risk: RiskCreate, current_user: User) -> Risk:
    """إنشاء مخاطرة جديدة"""
    db_risk = Risk(
        title=risk.title,
        description=risk.description,
        risk_type=risk.risk_type,
        probability=risk.probability,
        impact=risk.impact,
        business_unit=risk.business_unit,
        category=risk.category,
        source=risk.source,
        next_review_date=risk.next_review_date,
        owner_id=risk.owner_id,
        created_by_id=current_user.id
    )
    
    # حساب نتيجة ومستوى المخاطر
    db_risk.calculate_risk_score()
    
    db.add(db_risk)
    db.commit()
    db.refresh(db_risk)
    
    # إنشاء تقييم أولي
    create_initial_assessment(db, db_risk, current_user)
    
    return db_risk


def update_risk(db: Session, risk_id: int, risk_update: RiskUpdate, current_user: User) -> Optional[Risk]:
    """تحديث مخاطرة"""
    db_risk = get_risk(db, risk_id)
    if not db_risk:
        return None
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"] and db_risk.owner_id != current_user.id:
        return None
    
    update_data = risk_update.dict(exclude_unset=True)
    
    # تتبع التغييرات المهمة
    score_changed = False
    if 'probability' in update_data or 'impact' in update_data:
        score_changed = True
    
    for field, value in update_data.items():
        setattr(db_risk, field, value)
    
    # إعادة حساب النتيجة إذا تغيرت
    if score_changed:
        db_risk.calculate_risk_score()
        # إنشاء تقييم جديد
        create_assessment_from_risk(db, db_risk, current_user)
    
    db_risk.updated_by_id = current_user.id
    db.commit()
    db.refresh(db_risk)
    
    return db_risk


def delete_risk(db: Session, risk_id: int, current_user: User) -> bool:
    """حذف مخاطرة"""
    db_risk = get_risk(db, risk_id)
    if not db_risk:
        return False
    
    # التحقق من الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        return False
    
    db.delete(db_risk)
    db.commit()
    return True


def get_risk_summary(db: Session, current_user: User) -> Dict[str, Any]:
    """الحصول على ملخص المخاطر"""
    query = db.query(Risk)
    
    # فلترة حسب الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(Risk.owner_id == current_user.id)
    
    total_risks = query.count()
    
    # حسب المستوى
    by_level = {}
    for level in RiskLevel:
        count = query.filter(Risk.risk_level == level).count()
        by_level[level.value] = count
    
    # حسب النوع
    by_type = {}
    for risk_type in RiskType:
        count = query.filter(Risk.risk_type == risk_type).count()
        by_type[risk_type.value] = count
    
    # حسب الحالة
    by_status = {}
    for status in RiskStatus:
        count = query.filter(Risk.status == status).count()
        by_status[status.value] = count
    
    # المراجعات المتأخرة
    overdue_reviews = query.filter(
        and_(
            Risk.next_review_date.isnot(None),
            Risk.next_review_date < datetime.utcnow()
        )
    ).count()
    
    return {
        "total_risks": total_risks,
        "by_level": by_level,
        "by_type": by_type,
        "by_status": by_status,
        "overdue_reviews": overdue_reviews
    }


def get_risk_matrix(db: Session, current_user: User) -> List[List[Dict]]:
    """الحصول على مصفوفة المخاطر"""
    query = db.query(Risk).options(joinedload(Risk.owner))
    
    # فلترة حسب الصلاحيات
    if current_user.role not in ["ADMIN", "RISK_MANAGER"]:
        query = query.filter(Risk.owner_id == current_user.id)
    
    risks = query.all()
    
    # إنشاء مصفوفة 5x5
    matrix = []
    for impact in range(5, 0, -1):  # من 5 إلى 1
        row = []
        for probability in range(1, 6):  # من 1 إلى 5
            # البحث عن المخاطر في هذه الخلية
            cell_risks = [r for r in risks if r.probability == probability and r.impact == impact]
            
            # حساب مستوى المخاطر للخلية
            risk_score = probability * impact
            if risk_score <= 4:
                risk_level = RiskLevel.VERY_LOW
            elif risk_score <= 8:
                risk_level = RiskLevel.LOW
            elif risk_score <= 12:
                risk_level = RiskLevel.MEDIUM
            elif risk_score <= 16:
                risk_level = RiskLevel.HIGH
            elif risk_score <= 20:
                risk_level = RiskLevel.VERY_HIGH
            else:
                risk_level = RiskLevel.CRITICAL
            
            cell = {
                "probability": probability,
                "impact": impact,
                "risk_level": risk_level,
                "risk_count": len(cell_risks),
                "risks": cell_risks
            }
            row.append(cell)
        matrix.append(row)
    
    return matrix


# Risk Assessment CRUD
def create_initial_assessment(db: Session, risk: Risk, current_user: User):
    """إنشاء تقييم أولي للمخاطرة"""
    assessment = RiskAssessment(
        risk_id=risk.id,
        probability=risk.probability,
        impact=risk.impact,
        risk_score=risk.risk_score,
        risk_level=risk.risk_level,
        assessment_notes="تقييم أولي عند إنشاء المخاطرة",
        assessor_id=current_user.id
    )
    db.add(assessment)
    db.commit()


def create_assessment_from_risk(db: Session, risk: Risk, current_user: User):
    """إنشاء تقييم جديد من بيانات المخاطرة"""
    assessment = RiskAssessment(
        risk_id=risk.id,
        probability=risk.probability,
        impact=risk.impact,
        risk_score=risk.risk_score,
        risk_level=risk.risk_level,
        assessment_notes="تقييم محدث",
        assessor_id=current_user.id
    )
    db.add(assessment)
    db.commit()


def get_risk_assessments(db: Session, risk_id: int) -> List[RiskAssessment]:
    """الحصول على تقييمات المخاطرة"""
    return db.query(RiskAssessment).options(
        joinedload(RiskAssessment.assessor),
        joinedload(RiskAssessment.approved_by)
    ).filter(RiskAssessment.risk_id == risk_id).order_by(desc(RiskAssessment.assessment_date)).all()
