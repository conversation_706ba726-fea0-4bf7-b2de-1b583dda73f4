import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { ExclamationTriangleIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { authService } from '../services/auth'
import { ROUTES, VALIDATION_MESSAGES } from '../utils/constants'
import LoadingSpinner from '../components/common/LoadingSpinner'

const Login = ({ onLogin }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const onSubmit = async (data) => {
    setIsLoading(true)
    try {
      const result = await authService.login(data)
      toast.success('تم تسجيل الدخول بنجاح')
      onLogin(result.user)
      navigate(ROUTES.DASHBOARD)
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* الشعار والعنوان */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-white">
            نظام إدارة الحوكمة والمخاطر والامتثال
          </h2>
          <p className="mt-2 text-sm text-dark-400">
            يرجى تسجيل الدخول للوصول إلى النظام
          </p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <div className="card">
          <div className="card-body">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              {/* اسم المستخدم */}
              <div className="form-group">
                <label htmlFor="username" className="form-label">
                  اسم المستخدم
                </label>
                <input
                  id="username"
                  type="text"
                  autoComplete="username"
                  className={`input-field ${errors.username ? 'border-red-500' : ''}`}
                  placeholder="أدخل اسم المستخدم"
                  {...register('username', {
                    required: VALIDATION_MESSAGES.required,
                    minLength: {
                      value: 3,
                      message: VALIDATION_MESSAGES.minLength(3)
                    }
                  })}
                />
                {errors.username && (
                  <p className="form-error">{errors.username.message}</p>
                )}
              </div>

              {/* كلمة المرور */}
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  كلمة المرور
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    className={`input-field pl-10 ${errors.password ? 'border-red-500' : ''}`}
                    placeholder="أدخل كلمة المرور"
                    {...register('password', {
                      required: VALIDATION_MESSAGES.required,
                      minLength: {
                        value: 6,
                        message: VALIDATION_MESSAGES.minLength(6)
                      }
                    })}
                  />
                  <button
                    type="button"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5" />
                    ) : (
                      <EyeIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="form-error">{errors.password.message}</p>
                )}
              </div>

              {/* زر تسجيل الدخول */}
              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full btn-primary flex items-center justify-center"
                >
                  {isLoading ? (
                    <>
                      <LoadingSpinner size="small" className="ml-2" />
                      جاري تسجيل الدخول...
                    </>
                  ) : (
                    'تسجيل الدخول'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* معلومات المستخدم الافتراضي */}
        <div className="bg-dark-800 border border-dark-700 rounded-lg p-4">
          <h3 className="text-sm font-medium text-white mb-2">بيانات المدير الافتراضي:</h3>
          <div className="text-sm text-dark-300 space-y-1">
            <p><span className="font-medium">اسم المستخدم:</span> admin</p>
            <p><span className="font-medium">كلمة المرور:</span> Admin@123</p>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="text-center">
          <p className="text-xs text-dark-500">
            نظام متوافق مع معايير NCA ECC و ISO 27001
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
