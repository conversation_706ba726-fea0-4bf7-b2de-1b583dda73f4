# توثيق API - نظام إدارة الحوكمة والمخاطر والامتثال

## نظرة عامة

يوفر النظام API RESTful شامل لإدارة الحوادث السيبرانية والمستخدمين. جميع endpoints تتطلب مصادقة باستثناء تسجيل الدخول.

**Base URL**: `http://localhost:8000/api`

## المصادقة

### تسجيل الدخول
```http
POST /auth/login
Content-Type: multipart/form-data

{
  "username": "admin",
  "password": "Admin@123"
}
```

**الاستجابة**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### استخدام الرمز المميز
```http
Authorization: Bearer <access_token>
```

## endpoints المستخدمين

### الحصول على المستخدم الحالي
```http
GET /auth/me
Authorization: Bearer <token>
```

### تسجيل مستخدم جديد
```http
POST /auth/register
Content-Type: application/json

{
  "username": "user1",
  "password": "Password@123",
  "email": "<EMAIL>",
  "full_name": "اسم المستخدم"
}
```

### تغيير كلمة المرور
```http
POST /auth/change-password
Content-Type: application/json

{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

### قائمة المستخدمين (للمديرين فقط)
```http
GET /users?skip=0&limit=100
Authorization: Bearer <token>
```

### إنشاء مستخدم (للمديرين فقط)
```http
POST /users
Content-Type: application/json

{
  "username": "newuser",
  "password": "Password@123",
  "email": "<EMAIL>",
  "full_name": "المستخدم الجديد",
  "role": "user",
  "is_active": true
}
```

### تحديث مستخدم (للمديرين فقط)
```http
PUT /users/{user_id}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "full_name": "الاسم المحدث",
  "role": "admin",
  "is_active": false
}
```

### حذف مستخدم (للمديرين فقط)
```http
DELETE /users/{user_id}
Authorization: Bearer <token>
```

## endpoints الحوادث

### قائمة الحوادث
```http
GET /incidents?skip=0&limit=100&incident_type=malware&severity=high&status=open&search=تسريب
Authorization: Bearer <token>
```

**المعاملات الاختيارية**:
- `skip`: عدد السجلات المتجاهلة
- `limit`: عدد السجلات المطلوبة
- `incident_type`: نوع الحادث
- `severity`: مستوى الخطورة
- `status`: حالة الحادث
- `search`: البحث في العنوان والوصف

### الحصول على حادث محدد
```http
GET /incidents/{incident_id}
Authorization: Bearer <token>
```

### إنشاء حادث جديد
```http
POST /incidents
Content-Type: application/json

{
  "title": "تسريب بيانات العملاء",
  "description": "تم اكتشاف تسريب في قاعدة بيانات العملاء",
  "incident_type": "data_breach",
  "severity": "high",
  "incident_date": "2024-01-15T10:30:00Z",
  "affected_systems": "قاعدة بيانات العملاء",
  "impact_description": "تسريب معلومات 1000 عميل",
  "actions_taken": "تم إغلاق النظام فوراً",
  "nca_reported": "نعم"
}
```

### تحديث حادث
```http
PUT /incidents/{incident_id}
Content-Type: application/json

{
  "status": "resolved",
  "lessons_learned": "تحسين أمان قاعدة البيانات",
  "resolved_date": "2024-01-16T15:00:00Z"
}
```

### حذف حادث (للمديرين فقط)
```http
DELETE /incidents/{incident_id}
Authorization: Bearer <token>
```

### إحصائيات الحوادث
```http
GET /incidents/stats
Authorization: Bearer <token>
```

**الاستجابة**:
```json
{
  "total_incidents": 25,
  "open_incidents": 5,
  "resolved_incidents": 18,
  "critical_incidents": 2,
  "incidents_by_type": {
    "malware": 8,
    "phishing": 5,
    "data_breach": 3
  },
  "incidents_by_month": {
    "2024-01": 10,
    "2024-02": 15
  }
}
```

### الحوادث الأخيرة
```http
GET /incidents/summary/recent?limit=10
Authorization: Bearer <token>
```

## أنواع البيانات

### أنواع الحوادث
- `malware`: برمجيات خبيثة
- `phishing`: تصيد إلكتروني
- `data_breach`: تسريب بيانات
- `unauthorized_access`: وصول غير مصرح
- `denial_of_service`: حرمان من الخدمة
- `insider_threat`: تهديد داخلي
- `ransomware`: برمجيات الفدية
- `social_engineering`: هندسة اجتماعية
- `other`: أخرى

### مستويات الخطورة
- `low`: منخفض
- `medium`: متوسط
- `high`: عالي
- `critical`: حرج

### حالات الحوادث
- `open`: مفتوح
- `in_progress`: قيد المعالجة
- `resolved`: محلول
- `closed`: مغلق

### أدوار المستخدمين
- `admin`: مدير
- `user`: مستخدم عادي

## رموز الاستجابة

- `200`: نجح الطلب
- `201`: تم الإنشاء بنجاح
- `400`: خطأ في البيانات المرسلة
- `401`: غير مصرح (رمز مميز غير صالح)
- `403`: ممنوع (لا توجد صلاحية)
- `404`: غير موجود
- `422`: خطأ في التحقق من البيانات
- `500`: خطأ داخلي في الخادم

## أمثلة على الأخطاء

```json
{
  "detail": "رسالة الخطأ باللغة العربية"
}
```

```json
{
  "detail": [
    {
      "loc": ["body", "title"],
      "msg": "عنوان الحادث مطلوب",
      "type": "value_error"
    }
  ]
}
```

## فحص صحة النظام

```http
GET /health
```

**الاستجابة**:
```json
{
  "status": "healthy",
  "database": "connected",
  "version": "1.0.0"
}
```
